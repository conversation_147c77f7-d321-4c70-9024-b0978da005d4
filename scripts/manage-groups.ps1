# PowerShell script to manage Azure AD Groups
# This script helps you add, remove, and manage groups in your Terraform configuration

param(
    [Parameter(Mandatory=$false)]
    [string]$Action = "list",  # list, add, remove, update
    
    [Parameter(Mandatory=$false)]
    [string]$GroupName = "",
    
    [Parameter(Mandatory=$false)]
    [string]$DisplayName = "",
    
    [Parameter(Mandatory=$false)]
    [string]$Description = "",
    
    [Parameter(Mandatory=$false)]
    [string]$Roles = "",  # Comma-separated list of roles
    
    [Parameter(Mandatory=$false)]
    [string]$Members = "",  # Comma-separated list of user object IDs
    
    [Parameter(Mandatory=$false)]
    [string]$Environment = "default"  # default, production, development
)

# Function to read current groups from terraform.tfvars
function Read-GroupsFromTfVars {
    param([string]$FilePath)
    
    if (Test-Path $FilePath) {
        $content = Get-Content $FilePath -Raw
        Write-Host "Reading groups from: $FilePath"
        return $content
    } else {
        Write-Host "File not found: $FilePath"
        return $null
    }
}

# Function to add a new group
function Add-NewGroup {
    param(
        [string]$GroupName,
        [string]$DisplayName,
        [string]$Description,
        [string]$Roles,
        [string]$Members
    )
    
    $roleList = if ($Roles) { $Roles.Split(',') | ForEach-Object { $_.Trim() } } else @()
    $memberList = if ($Members) { $Members.Split(',') | ForEach-Object { $_.Trim() } } else @()
    
    $newGroup = @"
  $GroupName = {
    display_name         = "$DisplayName"
    description          = "$Description"
    security_enabled     = true
    assignable_to_role   = false
    azure_roles          = [$(if ($roleList.Count -gt 0) { '"' + ($roleList -join '", "') + '"' } else { '' })]
    members = [
      $(if ($memberList.Count -gt 0) { ($memberList | ForEach-Object { "# `"$_`"" }) -join "`n      " } else { '' })
    ]
  }
"@
    
    Write-Host "New group configuration:"
    Write-Host $newGroup
    return $newGroup
}

# Function to list current groups
function Show-CurrentGroups {
    param([string]$Content)
    
    if ($Content) {
        # Simple regex to extract group names
        $groupMatches = [regex]::Matches($Content, '(\w+)\s*=\s*{')
        Write-Host "`nCurrent groups:"
        foreach ($match in $groupMatches) {
            Write-Host "  - $($match.Groups[1].Value)"
        }
    }
}

# Main script logic
$tfvarsFile = "terraform.tfvars"
if ($Environment -ne "default") {
    $tfvarsFile = "terraform.tfvars.$Environment"
}

Write-Host "Managing Azure AD Groups for environment: $Environment"
Write-Host "Using file: $tfvarsFile"

switch ($Action.ToLower()) {
    "list" {
        $content = Read-GroupsFromTfVars -FilePath $tfvarsFile
        Show-CurrentGroups -Content $content
    }
    
    "add" {
        if (-not $GroupName -or -not $DisplayName -or -not $Description) {
            Write-Host "Error: GroupName, DisplayName, and Description are required for adding a group"
            Write-Host "Usage: .\manage-groups.ps1 -Action add -GroupName 'my_group' -DisplayName 'My Group' -Description 'Group description' -Roles 'Contributor,Reader' -Members 'user1-id,user2-id'"
            exit 1
        }
        
        $newGroup = Add-NewGroup -GroupName $GroupName -DisplayName $DisplayName -Description $Description -Roles $Roles -Members $Members
        Write-Host "`nTo add this group, manually insert the above configuration into your $tfvarsFile file"
        Write-Host "Place it before the closing brace of the groups block"
    }
    
    "remove" {
        if (-not $GroupName) {
            Write-Host "Error: GroupName is required for removing a group"
            Write-Host "Usage: .\manage-groups.ps1 -Action remove -GroupName 'my_group'"
            exit 1
        }
        
        Write-Host "To remove group '$GroupName', manually delete the corresponding block from your $tfvarsFile file"
    }
    
    "update" {
        if (-not $GroupName) {
            Write-Host "Error: GroupName is required for updating a group"
            Write-Host "Usage: .\manage-groups.ps1 -Action update -GroupName 'my_group' -DisplayName 'New Name' -Description 'New description'"
            exit 1
        }
        
        Write-Host "To update group '$GroupName', manually modify the corresponding block in your $tfvarsFile file"
    }
    
    default {
        Write-Host "Unknown action: $Action"
        Write-Host "Available actions: list, add, remove, update"
        Write-Host "Usage examples:"
        Write-Host "  .\manage-groups.ps1 -Action list"
        Write-Host "  .\manage-groups.ps1 -Action add -GroupName 'new_team' -DisplayName 'New Team' -Description 'New team description'"
        Write-Host "  .\manage-groups.ps1 -Action remove -GroupName 'old_team'"
        Write-Host "  .\manage-groups.ps1 -Action list -Environment production"
    }
}

Write-Host "`nRemember to run 'terraform plan' and 'terraform apply' after making changes to your configuration" 