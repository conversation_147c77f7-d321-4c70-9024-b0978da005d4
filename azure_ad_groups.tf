# Get current Azure AD client config
data "azuread_client_config" "current" {}

# Get current subscription
data "azurerm_client_config" "current" {}

# Get all subscriptions if assigning to all
data "azurerm_subscriptions" "available" {
  count = var.assign_roles_to_all_subscriptions ? 1 : 0
}

# Create Azure AD Group for Infrastructure Team Super Admin
resource "azuread_group" "infras_team_super_admin" {
  count               = var.create_infras_team_group ? 1 : 0
  display_name        = "Infras Team - Super Admin"
  description         = "Infrastructure Team with Owner privileges"
  security_enabled    = true
  assignable_to_role  = false
  owners              = [data.azuread_client_config.current.object_id]
  members             = var.infras_team_group_members
}

# Assign Owner role to all subscriptions (if enabled)
resource "azurerm_role_assignment" "infras_team_owner_all_subs" {
  count               = var.create_infras_team_group && var.assign_roles_to_all_subscriptions ? length(data.azurerm_subscriptions.available[0].subscriptions) : 0
  scope               = "/subscriptions/${data.azurerm_subscriptions.available[0].subscriptions[count.index].subscription_id}"
  role_definition_name = "Owner"
  principal_id        = azuread_group.infras_team_super_admin[0].object_id
}

# Assign Owner role to current subscription (if not assigning to all)
resource "azurerm_role_assignment" "infras_team_owner_current_sub" {
  count               = var.create_infras_team_group && !var.assign_roles_to_all_subscriptions ? 1 : 0
  scope               = "/subscriptions/${data.azurerm_client_config.current.subscription_id}"
  role_definition_name = "Owner"
  principal_id        = azuread_group.infras_team_super_admin[0].object_id
}

# Outputs
output "infras_team_super_admin_group" {
  description = "Info about the Infras Team Super Admin group"
  value = var.create_infras_team_group ? {
    object_id    = azuread_group.infras_team_super_admin[0].object_id
    display_name = azuread_group.infras_team_super_admin[0].display_name
    description  = azuread_group.infras_team_super_admin[0].description
  } : null
}

output "subscription_role_assignments" {
  description = "List of subscription IDs where Owner role has been assigned"
  value = var.create_infras_team_group ? concat(
    [for assignment in azurerm_role_assignment.infras_team_owner_all_subs : assignment.scope],
    [for assignment in azurerm_role_assignment.infras_team_owner_current_sub : assignment.scope]
  ) : []
}
