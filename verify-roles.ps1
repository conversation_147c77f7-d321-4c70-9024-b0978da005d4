# PowerShell script to verify Azure role names
# Run this script to check if the role names in your configuration are correct

Write-Host "=== Azure Role Names Verification ===" -ForegroundColor Green
Write-Host ""

# Check if Azure CLI is installed
try {
    $azVersion = az version --output json | ConvertFrom-Json
    Write-Host "✅ Azure CLI version: $($azVersion.'azure-cli')" -ForegroundColor Green
} catch {
    Write-Host "❌ Azure CLI not found. Please install Azure CLI first." -ForegroundColor Red
    exit 1
}

# Check if logged in
try {
    $account = az account show --output json | ConvertFrom-Json
    Write-Host "✅ Logged in as: $($account.user.name)" -ForegroundColor Green
    Write-Host ""
} catch {
    Write-Host "❌ Not logged in to Azure. Please run 'az login' first." -ForegroundColor Red
    exit 1
}

# Roles to verify from our configuration
$rolesToVerify = @(
    "Owner",
    "Contributor", 
    "Reader",
    "Security Admin",
    "User Access Administrator",
    "Attestation Contributor", 
    "Key Vault Administrator",
    "Virtual Machine Administrator Login",
    "Virtual Machine User Login",
    "Reader and Data Access",
    "Storage Blob Data Reader",
    "Azure Kubernetes Service Contributor Role"
)

Write-Host "=== Verifying Role Names ===" -ForegroundColor Yellow
Write-Host ""

$validRoles = @()
$invalidRoles = @()

foreach ($role in $rolesToVerify) {
    try {
        $roleDefinition = az role definition list --name $role --query "[0].{Name:roleName, Id:name}" --output json | ConvertFrom-Json
        if ($roleDefinition) {
            Write-Host "✅ $role" -ForegroundColor Green
            $validRoles += $role
        } else {
            Write-Host "❌ $role (not found)" -ForegroundColor Red
            $invalidRoles += $role
        }
    } catch {
        Write-Host "❌ $role (error checking)" -ForegroundColor Red
        $invalidRoles += $role
    }
}

Write-Host ""
Write-Host "=== Summary ===" -ForegroundColor Yellow
Write-Host "Valid roles: $($validRoles.Count)" -ForegroundColor Green
Write-Host "Invalid roles: $($invalidRoles.Count)" -ForegroundColor Red

if ($invalidRoles.Count -gt 0) {
    Write-Host ""
    Write-Host "=== Invalid Roles - Suggested Alternatives ===" -ForegroundColor Yellow
    
    foreach ($invalidRole in $invalidRoles) {
        Write-Host ""
        Write-Host "❌ Invalid: $invalidRole" -ForegroundColor Red
        
        # Search for similar roles
        $searchTerm = $invalidRole.Split(' ')[0]  # Get first word for search
        try {
            $similarRoles = az role definition list --query "[?contains(roleName, '$searchTerm')].{Name:roleName, Id:name}" --output json | ConvertFrom-Json
            if ($similarRoles.Count -gt 0) {
                Write-Host "   💡 Similar roles found:" -ForegroundColor Yellow
                foreach ($similar in $similarRoles | Select-Object -First 5) {
                    Write-Host "      - $($similar.Name)" -ForegroundColor Cyan
                }
            }
        } catch {
            Write-Host "   ⚠️ Could not search for similar roles" -ForegroundColor Yellow
        }
    }
}

Write-Host ""
Write-Host "=== Common Role Categories ===" -ForegroundColor Yellow

# Show common roles by category
$categories = @{
    "Virtual Machine" = "Virtual Machine"
    "Storage" = "Storage"
    "Kubernetes" = "Kubernetes"
    "Security" = "Security"
    "Key Vault" = "Key Vault"
}

foreach ($category in $categories.Keys) {
    Write-Host ""
    Write-Host "--- $category Roles ---" -ForegroundColor Cyan
    try {
        $categoryRoles = az role definition list --query "[?contains(roleName, '$($categories[$category])')].roleName" --output json | ConvertFrom-Json
        foreach ($role in $categoryRoles | Select-Object -First 10) {
            Write-Host "  - $role"
        }
    } catch {
        Write-Host "  ⚠️ Could not list $category roles" -ForegroundColor Yellow
    }
}

Write-Host ""
Write-Host "=== Useful Commands ===" -ForegroundColor Yellow
Write-Host "List all roles: az role definition list --query '[].roleName' -o table"
Write-Host "Search roles: az role definition list --query \"[?contains(roleName, 'Virtual')].roleName\" -o table"
Write-Host "Get role details: az role definition list --name 'Role Name' --query '[0]' -o json"
Write-Host ""
Write-Host "📚 For complete role reference: https://docs.microsoft.com/en-us/azure/role-based-access-control/built-in-roles" -ForegroundColor Green
