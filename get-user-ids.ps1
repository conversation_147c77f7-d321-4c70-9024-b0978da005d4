# PowerShell script to get Azure AD user object IDs
# Run this script to get user object IDs for your terraform.tfvars

Write-Host "=== Azure AD User Object ID Helper ===" -ForegroundColor Green
Write-Host ""

# Check if Azure CLI is installed
try {
    $azVersion = az version --output json | ConvertFrom-Json
    Write-Host "✅ Azure CLI version: $($azVersion.'azure-cli')" -ForegroundColor Green
} catch {
    Write-Host "❌ Azure CLI not found. Please install Azure CLI first." -ForegroundColor Red
    Write-Host "Download from: https://docs.microsoft.com/en-us/cli/azure/install-azure-cli"
    exit 1
}

# Check if logged in
try {
    $account = az account show --output json | ConvertFrom-Json
    Write-Host "✅ Logged in as: $($account.user.name)" -ForegroundColor Green
    Write-Host "✅ Tenant: $($account.tenantId)" -ForegroundColor Green
    Write-Host ""
} catch {
    Write-Host "❌ Not logged in to Azure. Please run 'az login' first." -ForegroundColor Red
    exit 1
}

# Function to get user by email
function Get-UserByEmail {
    param($email)
    try {
        $user = az ad user show --id $email --query "{DisplayName:displayName, ObjectId:id, UserPrincipalName:userPrincipalName}" --output json | ConvertFrom-Json
        return $user
    } catch {
        return $null
    }
}

# Function to search users by display name
function Search-UsersByName {
    param($name)
    try {
        $users = az ad user list --filter "startswith(displayName,'$name')" --query "[].{DisplayName:displayName, ObjectId:id, UserPrincipalName:userPrincipalName}" --output json | ConvertFrom-Json
        return $users
    } catch {
        return @()
    }
}

# Get current user
Write-Host "=== Current User ===" -ForegroundColor Yellow
try {
    $currentUser = az ad signed-in-user show --query "{DisplayName:displayName, ObjectId:id, UserPrincipalName:userPrincipalName}" --output json | ConvertFrom-Json
    Write-Host "Display Name: $($currentUser.DisplayName)"
    Write-Host "Object ID: $($currentUser.ObjectId)" -ForegroundColor Cyan
    Write-Host "UPN: $($currentUser.UserPrincipalName)"
    Write-Host ""
} catch {
    Write-Host "❌ Could not get current user information" -ForegroundColor Red
}

# Interactive user lookup
do {
    Write-Host "=== User Lookup Options ===" -ForegroundColor Yellow
    Write-Host "1. Search by email/UPN"
    Write-Host "2. Search by display name"
    Write-Host "3. List all users (first 50)"
    Write-Host "4. Generate terraform.tfvars template"
    Write-Host "5. Exit"
    Write-Host ""
    
    $choice = Read-Host "Select an option (1-5)"
    
    switch ($choice) {
        "1" {
            $email = Read-Host "Enter email/UPN"
            $user = Get-UserByEmail $email
            if ($user) {
                Write-Host ""
                Write-Host "✅ User found:" -ForegroundColor Green
                Write-Host "Display Name: $($user.DisplayName)"
                Write-Host "Object ID: $($user.ObjectId)" -ForegroundColor Cyan
                Write-Host "UPN: $($user.UserPrincipalName)"
                Write-Host ""
                
                # Copy to clipboard if available
                try {
                    $user.ObjectId | Set-Clipboard
                    Write-Host "📋 Object ID copied to clipboard!" -ForegroundColor Green
                } catch {
                    Write-Host "💡 Copy this Object ID: $($user.ObjectId)" -ForegroundColor Yellow
                }
            } else {
                Write-Host "❌ User not found" -ForegroundColor Red
            }
            Write-Host ""
        }
        
        "2" {
            $name = Read-Host "Enter display name (partial match)"
            $users = Search-UsersByName $name
            if ($users.Count -gt 0) {
                Write-Host ""
                Write-Host "✅ Found $($users.Count) user(s):" -ForegroundColor Green
                foreach ($user in $users) {
                    Write-Host "---"
                    Write-Host "Display Name: $($user.DisplayName)"
                    Write-Host "Object ID: $($user.ObjectId)" -ForegroundColor Cyan
                    Write-Host "UPN: $($user.UserPrincipalName)"
                }
            } else {
                Write-Host "❌ No users found" -ForegroundColor Red
            }
            Write-Host ""
        }
        
        "3" {
            Write-Host ""
            Write-Host "📋 First 50 users:" -ForegroundColor Yellow
            try {
                $allUsers = az ad user list --top 50 --query "[].{DisplayName:displayName, ObjectId:id, UserPrincipalName:userPrincipalName}" --output json | ConvertFrom-Json
                foreach ($user in $allUsers) {
                    Write-Host "Display Name: $($user.DisplayName.PadRight(30)) | Object ID: $($user.ObjectId) | UPN: $($user.UserPrincipalName)"
                }
            } catch {
                Write-Host "❌ Could not list users" -ForegroundColor Red
            }
            Write-Host ""
        }
        
        "4" {
            Write-Host ""
            Write-Host "=== Terraform Variables Template ===" -ForegroundColor Yellow
            Write-Host ""
            Write-Host "# Add these to your terraform.tfvars file:" -ForegroundColor Green
            Write-Host ""
            Write-Host 'azure_ad_groups = {'
            Write-Host '  infras_team_super_admin = {'
            Write-Host '    display_name         = "Infras Team - Super Admin"'
            Write-Host '    description          = "Infrastructure Team with Super Admin privileges"'
            Write-Host '    security_enabled     = true'
            Write-Host '    assignable_to_role   = true'
            Write-Host '    azure_roles          = ["Owner"]'
            Write-Host '    directory_roles      = ["Global Administrator"]'
            Write-Host '    members = ['
            Write-Host '      "USER-OBJECT-ID-1",  # Replace with actual Object IDs' -ForegroundColor Yellow
            Write-Host '      "USER-OBJECT-ID-2",  # Replace with actual Object IDs' -ForegroundColor Yellow
            Write-Host '    ]'
            Write-Host '  }'
            Write-Host '}'
            Write-Host ""
            Write-Host "💡 Replace USER-OBJECT-ID-1, USER-OBJECT-ID-2 with actual Object IDs from above" -ForegroundColor Yellow
            Write-Host ""
        }
        
        "5" {
            Write-Host "👋 Goodbye!" -ForegroundColor Green
            break
        }
        
        default {
            Write-Host "❌ Invalid option. Please select 1-5." -ForegroundColor Red
            Write-Host ""
        }
    }
} while ($choice -ne "5")

Write-Host ""
Write-Host "=== Useful Commands ===" -ForegroundColor Yellow
Write-Host "Get user by email: az ad user show --id <EMAIL> --query id -o tsv"
Write-Host "Get current user: az ad signed-in-user show --query id -o tsv"
Write-Host "List all users: az ad user list --query '[].{DisplayName:displayName, ObjectId:id}' -o table"
Write-Host ""
Write-Host "📚 For more examples, see: terraform.tfvars.multi-groups-example" -ForegroundColor Green
