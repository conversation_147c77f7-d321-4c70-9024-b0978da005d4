# Local .terraform directories
**/.terraform/*

# .tfstate files
*.tfstate
*.tfstate.*

# Lock files
.terraform.lock.hcl

# Crash log files
crash.log

# Ignore any .tfvars files that are generated automatically for each Terraform run. Most
# .tfvars files are managed as part of configuration and so should be included in
# version control.
#
# example.tfvars

# Ignore override files as they are usually used to override resources locally and so
# are not checked in
override.tf
override.tf.json
*_override.tf
*_override.tf.json

# Ignore tfvars for OPA testing
**/opa.auto.tfvars.json

# Include override files you do wish to add to version control using negated pattern
#
# !example_override.tf

# Include tfplan files to ignore the plan output of command: terraform plan -out=tfplan
# example: *tfplan*
*tfplan*

# Ignore any files with .ignore. in the filename
*.ignore.*

# Ignore test_local
tests/modules/test_local

# Ignore macOS .DS_Store files which are generated automatically by Finder.
.DS_Store

# Ignore Visual Studio Code settings config
**/.vscode/settings.json

# Ignore Super-Linter log
**/super-linter.report
**/super-linter.report/*
**/super-linter.log
.vscode/launch.json
.github/scripts/Template.Parser.Cli.exe
examples/400-multi-with-orchestration/*.auto.tfvars
