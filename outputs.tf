output "enterprise_scale" {
  description = "Configuration data for the Azure landing zone."
  value       = module.enterprise_scale
}

output "azure_ad_groups" {
  description = "Information about created Azure AD groups"
  value       = module.azure_ad_groups.groups
}

output "azure_ad_group_object_ids" {
  description = "Map of group keys to their object IDs"
  value       = module.azure_ad_groups.group_object_ids
}

output "azure_role_assignments" {
  description = "List of Azure RBAC role assignments created"
  value       = module.azure_ad_groups.azure_role_assignments
}

output "directory_role_assignments" {
  description = "List of Azure AD directory role assignments created"
  value       = module.azure_ad_groups.directory_role_assignments
}
