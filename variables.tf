# Use variables to customize the deployment

variable "root_id" {
  type    = string
  default = "myorg"
}

variable "root_name" {
  type    = string
  default = "My Organization"
}

variable "azure_ad_groups" {
  description = "Map of Azure AD groups to create with their configurations"
  type = map(object({
    display_name         = string
    description          = string
    security_enabled     = bool
    assignable_to_role   = bool
    mail_enabled         = optional(bool, false)
    members              = optional(list(string), [])
    additional_owners    = optional(list(string), [])
    azure_roles          = optional(list(string), [])
    directory_roles      = optional(list(string), [])
  }))
  default = {
    # Infrastructure Teams
    infras_team_super_admin = {
      display_name         = "Infras Team - Super Admin"
      description          = "Infrastructure Team with Super Admin privileges including Owner, Enterprise Administrator, and EA Purchaser roles"
      security_enabled     = true
      assignable_to_role   = true
      azure_roles          = ["Owner"]
      directory_roles      = ["Global Administrator"]
      members              = []
    }

    infras_team_admin = {
      display_name         = "Infras Team - Admin"
      description          = "Infrastructure Team with administrative access"
      security_enabled     = true
      assignable_to_role   = false
      azure_roles          = ["Contributor"]
      members              = []
    }

    infras_team_operation = {
      display_name         = "Infras Team - Operation"
      description          = "Infrastructure Team with read-only operational access"
      security_enabled     = true
      assignable_to_role   = false
      azure_roles          = ["Reader"]
      members              = []
    }

    infras_team_breakglass = {
      display_name         = "Infras Team - Breakglass"
      description          = "Infrastructure Team emergency access with Owner, Enterprise Administrator, and EA Purchaser roles"
      security_enabled     = true
      assignable_to_role   = true
      azure_roles          = ["Owner"]
      directory_roles      = ["Global Administrator"]
      members              = []
    }

variable "assign_roles_to_all_subscriptions" {
  type        = bool
  description = "If set to true, will assign Azure roles to all subscriptions in the tenant. If false, only assigns to current subscription."
  default     = false
}

variable "enable_directory_role_assignments" {
  type        = bool
  description = "If set to true, will enable Azure AD directory role assignments. Requires Privileged Role Administrator permissions."
  default     = false
}