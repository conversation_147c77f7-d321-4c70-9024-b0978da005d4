# Production Environment Configuration
# Use this file for production deployment

root_id   = "myorg-prod"
root_name = "My Organization - Production"

# Production Azure AD Groups
groups = {
  infras_team_super_admin = {
    display_name         = "Infras Team - Super Admin (Prod)"
    description          = "Infrastructure Team with Super Admin privileges for Production"
    security_enabled     = true
    assignable_to_role   = true
    azure_roles          = ["Owner"]
    directory_roles      = ["Global Administrator"]
    members = [
      # Add production admin user IDs here
    ]
  }

  production_developers = {
    display_name         = "Production Developers"
    description          = "Production development team with limited contributor access"
    security_enabled     = true
    assignable_to_role   = false
    azure_roles          = ["Contributor"]
    members = [
      # Add production developer user IDs here
    ]
  }

  production_readers = {
    display_name         = "Production Readers"
    description          = "Production read-only access for monitoring"
    security_enabled     = true
    assignable_to_role   = false
    azure_roles          = ["Reader"]
    members = [
      # Add production reader user IDs here
    ]
  }

  production_security = {
    display_name         = "Production Security Team"
    description          = "Production security team with security administrator privileges"
    security_enabled     = true
    assignable_to_role   = true
    azure_roles          = ["Security Administrator"]
    directory_roles      = ["Security Administrator"]
    members = [
      # Add production security team user IDs here
    ]
  }

  production_operations = {
    display_name         = "Production Operations"
    description          = "Production operations team with monitoring and backup access"
    security_enabled     = true
    assignable_to_role   = false
    azure_roles          = ["Monitoring Contributor", "Backup Contributor"]
    members = [
      # Add production operations user IDs here
    ]
  }
}

# Production settings
assign_roles_to_all_subscriptions = false
enable_directory_role_assignments = true 