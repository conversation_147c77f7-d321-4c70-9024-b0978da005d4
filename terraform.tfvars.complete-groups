# Complete Azure AD Groups Configuration
# Copy this to terraform.tfvars and add your user object IDs

# Basic configuration
root_id   = "myorg"
root_name = "My Organization"

# Complete Azure AD Groups configuration based on requirements
azure_ad_groups = {
  # ===== INFRASTRUCTURE TEAMS =====
  infras_team_super_admin = {
    display_name         = "Infras Team - Super Admin"
    description          = "Infrastructure Team with Super Admin privileges including Owner, Enterprise Administrator, and EA Purchaser roles"
    security_enabled     = true
    assignable_to_role   = true              # Required for Global Administrator
    azure_roles          = ["Owner"]
    directory_roles      = ["Global Administrator"]  # EA: Enterprise administrator, EA: EA purchaser handled via EA portal
    members = [
      # "user1-object-id",                   # Add actual user object IDs here
      # "user2-object-id",
    ]
  }
  
  infras_team_admin = {
    display_name         = "Infras Team - Admin"
    description          = "Infrastructure Team with administrative access"
    security_enabled     = true
    assignable_to_role   = false
    azure_roles          = ["Contributor"]
    members = [
      # "admin1-object-id",
      # "admin2-object-id",
    ]
  }
  
  infras_team_operation = {
    display_name         = "Infras Team - Operation"
    description          = "Infrastructure Team with read-only operational access"
    security_enabled     = true
    assignable_to_role   = false
    azure_roles          = ["Reader"]
    members = [
      # "ops1-object-id",
      # "ops2-object-id",
    ]
  }
  
  infras_team_breakglass = {
    display_name         = "Infras Team - Breakglass"
    description          = "Infrastructure Team emergency access with Owner, Enterprise Administrator, and EA Purchaser roles"
    security_enabled     = true
    assignable_to_role   = true              # Required for Global Administrator
    azure_roles          = ["Owner"]
    directory_roles      = ["Global Administrator"]  # EA: Enterprise administrator, EA: EA purchaser handled via EA portal
    members = [
      # "breakglass1-object-id",
      # "breakglass2-object-id",
    ]
  }
  
  # ===== APPLICATION TEAMS =====
  app_teams_super_admin = {
    display_name         = "App Teams - Super Admin"
    description          = "Application Teams with Super Admin privileges at Resource Group scope"
    security_enabled     = true
    assignable_to_role   = false
    azure_roles          = ["Contributor"]  # Resource Group scope
    members = [
      # "app-super-admin1-object-id",
      # "app-super-admin2-object-id",
    ]
  }
  
  app_teams_admin = {
    display_name         = "App Teams - Admin"
    description          = "Application Teams with administrative access including VM and storage access"
    security_enabled     = true
    assignable_to_role   = false
    azure_roles          = [
      "Virtual Machine Administrator Login",
      "Virtual Machine User Login", 
      "Reader and Data Access",
      "Storage Blob Data Reader",
      "Azure Kubernetes Service Contributor Role"
    ]  # Resource Group scope
    members = [
      # "app-admin1-object-id",
      # "app-admin2-object-id",
    ]
  }
  
  app_teams_operation = {
    display_name         = "App Teams - Operation"
    description          = "Application Teams with read-only operational access at Resource Group scope"
    security_enabled     = true
    assignable_to_role   = false
    azure_roles          = ["Reader"]  # Resource Group scope
    members = [
      # "app-ops1-object-id",
      # "app-ops2-object-id",
    ]
  }
  
  app_teams_breakglass = {
    display_name         = "App Teams - Breakglass"
    description          = "Application Teams emergency access with Contributor at Resource Group scope"
    security_enabled     = true
    assignable_to_role   = false
    azure_roles          = ["Contributor"]  # Resource Group scope
    members = [
      # "app-breakglass1-object-id",
      # "app-breakglass2-object-id",
    ]
  }
  
  # ===== SECURITY TEAM =====
  security_team = {
    display_name         = "Security Team"
    description          = "Quản lý cấu hình bảo mật"
    security_enabled     = true
    assignable_to_role   = false
    azure_roles          = [
      "Security Admin",
      "User Access Administrator", 
      "Attestation Contributor",
      "Key Vault Administrator",
      "Reader"
    ]  # Resource Group scope
    members = [
      # "security1-object-id",
      # "security2-object-id",
    ]
  }
  
  # ===== FINANCE TEAM =====
  finance_team = {
    display_name         = "Finance Team"
    description          = "Quản lý billing"
    security_enabled     = true
    assignable_to_role   = false
    azure_roles          = ["Reader"]  # Management Group scope
    # Note: EA: Department Administrator (read only) is managed through EA portal
    members = [
      # "finance1-object-id",
      # "finance2-object-id",
    ]
  }
}

# Role assignment scope
# Set to true to assign Azure roles to all subscriptions in the tenant
# Set to false to assign only to the current subscription
assign_roles_to_all_subscriptions = false

# Directory role assignments
# Set to true to enable Azure AD directory role assignments
# Requires Privileged Role Administrator permissions
enable_directory_role_assignments = false

# ===== IMPORTANT NOTES =====
# 1. EA (Enterprise Agreement) roles like "Enterprise administrator" and "EA purchaser" 
#    must be assigned through the Azure EA portal, not through Terraform
# 
# 2. Resource Group and Management Group scoped roles will be assigned at subscription level
#    in this configuration. For true Resource Group scoping, additional configuration is needed
#
# 3. To get user object IDs, use:
#    az ad user show --id <EMAIL> --query id -o tsv
#    az ad signed-in-user show --query id -o tsv
#    az ad user list --query "[].{DisplayName:displayName, ObjectId:id}" -o table
#
# 4. Some roles like "Virtual Machine Local User Login" might need to be verified for exact name
#    Use: az role definition list --query "[?contains(roleName, 'Virtual Machine')].{Name:roleName, Id:name}" -o table
