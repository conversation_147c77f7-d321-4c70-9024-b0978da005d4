# Example: Using the Azure AD Groups module with multiple groups

module "azure_ad_groups_example" {
  source = "../modules/azure-ad-groups"

  groups = {
    # Infrastructure Team with Super Admin privileges
    infras_team_super_admin = {
      display_name     = "Infras Team - Super Admin"
      description      = "Infrastructure Team with Super Admin privileges"
      security_enabled = true
      members          = ["user1-object-id", "user2-object-id"]
      owners           = ["owner1-object-id"]
    }
    
    # Development Team with Contributor access
    dev_team = {
      display_name     = "Development Team"
      description      = "Development team with Contributor access"
      security_enabled = true
      members          = ["dev1-object-id", "dev2-object-id", "dev3-object-id"]
    }
    
    # Operations Team with Reader access
    ops_team = {
      display_name     = "Operations Team"
      description      = "Operations team with Reader access"
      security_enabled = true
      members          = ["ops1-object-id", "ops2-object-id"]
    }
    
    # Security Team with Security Admin access
    security_team = {
      display_name     = "Security Team"
      description      = "Security team with Security Administrator access"
      security_enabled = true
      members          = ["sec1-object-id", "sec2-object-id"]
    }
  }

  # Azure RBAC Role Assignments
  role_assignments = [
    {
      group_key  = "infras_team_super_admin"
      role_name  = "Owner"
      scope      = "current"
      scope_type = "subscription"
    },
    {
      group_key  = "dev_team"
      role_name  = "Contributor"
      scope      = "current"
      scope_type = "subscription"
    },
    {
      group_key  = "ops_team"
      role_name  = "Reader"
      scope      = "current"
      scope_type = "subscription"
    },
    {
      group_key  = "security_team"
      role_name  = "Security Administrator"
      scope      = "current"
      scope_type = "subscription"
    }
  ]

  # Azure AD Directory Role Assignments
  azure_ad_role_assignments = [
    {
      group_key = "infras_team_super_admin"
      role_name = "Global Administrator"
    },
    {
      group_key = "security_team"
      role_name = "Security Administrator"
    }
  ]

  assign_roles_to_all_subscriptions = false
}

# Outputs
output "all_groups" {
  description = "Information about all created Azure AD groups"
  value       = module.azure_ad_groups_example.groups
}

output "subscription_assignments" {
  description = "List of subscription role assignments"
  value       = module.azure_ad_groups_example.subscription_role_assignments
}

output "azure_ad_assignments" {
  description = "List of Azure AD directory role assignments"
  value       = module.azure_ad_groups_example.azure_ad_role_assignments
} 