# Example Terraform variables file
# Copy this file to terraform.tfvars and customize the values

# Basic configuration
root_id   = "myorg"
root_name = "My Organization"

# Azure AD Groups configuration
groups = {
  infras_team_super_admin = {
    display_name         = "Infras Team - Super Admin"
    description          = "Infrastructure Team with Super Admin privileges including Owner, Enterprise Administrator, and EA Purchaser roles"
    security_enabled     = true
    assignable_to_role   = true
    azure_roles          = ["Owner"]
    directory_roles      = ["Global Administrator"]
    members = [
      # "00000000-0000-0000-0000-000000000000",  # User 1 Object ID
      # "11111111-1111-1111-1111-111111111111",  # User 2 Object ID
    ]
    additional_owners = [
      # "22222222-2222-2222-2222-222222222222",  # Additional Owner Object ID
    ]
  }

  # Example: Additional groups
  # developers = {
  #   display_name         = "Developers"
  #   description          = "Development team with contributor access"
  #   security_enabled     = true
  #   assignable_to_role   = false
  #   azure_roles          = ["Contributor"]
  #   members = [
  #     "dev1-object-id",
  #     "dev2-object-id"
  #   ]
  # }

  # readers = {
  #   display_name         = "Readers"
  #   description          = "Read-only access group"
  #   security_enabled     = true
  #   assignable_to_role   = false
  #   azure_roles          = ["Reader"]
  # }
}

# Role assignment scope
# Set to true to assign Azure roles to all subscriptions in the tenant
# Set to false to assign only to the current subscription
assign_roles_to_all_subscriptions = false

# Directory role assignments
# Set to true to enable Azure AD directory role assignments
# Requires Privileged Role Administrator permissions
enable_directory_role_assignments = false
