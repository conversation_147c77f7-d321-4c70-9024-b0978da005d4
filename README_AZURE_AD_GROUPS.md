# Azure AD Groups and Role Assignments

This document describes the Azure AD group management functionality added to the Azure Landing Zones deployment.

## Overview

The configuration creates an Azure AD group called "Infras Team - Super Admin" with the following roles:
- **Owner** role at subscription level
- **Global Administrator** (Enterprise Administrator) role at Azure AD level
- **EA Purchaser** role (Note: This is managed through Azure EA portal, not Terraform)

## Files Added

### 1. `azure_ad_groups.tf`
Contains the main Azure AD group and role assignment resources:
- Creates the "Infras Team - Super Admin" Azure AD group
- Assigns Owner role at subscription level
- Assigns Global Administrator role at Azure AD level
- Includes conditional logic based on variables

### 2. Updated `terraform.tf`
Added Azure AD provider configuration:
```hcl
azuread = {
  source  = "hashicorp/azuread"
  version = "~> 2.47"
}
```

### 3. Updated `variables.tf`
Added new variables for group management:
- `create_infras_team_group`: Enable/disable group creation
- `infras_team_group_members`: List of user object IDs to add as members
- `assign_roles_to_all_subscriptions`: Control role assignment scope

### 4. `terraform.tfvars.example`
Example configuration file showing how to use the new variables.

## Configuration Variables

| Variable | Type | Default | Description |
|----------|------|---------|-------------|
| `create_infras_team_group` | bool | `true` | Create the Infras Team Super Admin group |
| `infras_team_group_members` | list(string) | `[]` | User object IDs to add as group members |
| `assign_roles_to_all_subscriptions` | bool | `false` | Assign Owner role to all subscriptions vs current only |

## Usage

### 1. Basic Setup
```hcl
# In terraform.tfvars
create_infras_team_group = true
```

### 2. Add Users to Group
```hcl
# Get user object IDs from Azure AD
infras_team_group_members = [
  "12345678-1234-1234-1234-123456789012",
  "87654321-4321-4321-4321-210987654321"
]
```

### 3. Assign to All Subscriptions
```hcl
assign_roles_to_all_subscriptions = true
```

## Getting User Object IDs

You can get user object IDs using Azure CLI:
```bash
# Get current user
az ad signed-in-user show --query id -o tsv

# Get specific user
az ad user show --id <EMAIL> --query id -o tsv

# List all users
az ad user list --query "[].{DisplayName:displayName, ObjectId:id}" -o table
```

## Role Assignments Created

### Azure RBAC Roles
- **Owner**: Full access to Azure resources at subscription level
  - Scope: `/subscriptions/{subscription-id}`
  - Assigned to: Infras Team - Super Admin group

### Azure AD Roles
- **Global Administrator**: Full access to Azure AD and Microsoft 365 services
  - Scope: Azure AD tenant level
  - Assigned to: Infras Team - Super Admin group

### EA Purchaser Role
The EA Purchaser role is related to Enterprise Agreement billing and must be assigned through the Azure EA portal. This role allows:
- Purchasing Azure services under an Enterprise Agreement
- Managing billing and cost allocation
- Cannot be assigned through Terraform

## Security Considerations

1. **Global Administrator Role**: This is a highly privileged role. Only assign to trusted users.
2. **Owner Role**: Provides full access to Azure resources. Consider using more restrictive roles if possible.
3. **Group Membership**: Regularly review group membership and remove users who no longer need access.
4. **Conditional Access**: Consider implementing conditional access policies for high-privilege groups.

## Deployment

1. Copy `terraform.tfvars.example` to `terraform.tfvars`
2. Customize the variables as needed
3. Run `terraform init` to install the Azure AD provider
4. Run `terraform plan` to review changes
5. Run `terraform apply` to create the resources

## Outputs

The configuration provides the following outputs:
- `infras_team_super_admin_group`: Group information (object ID, display name, description)
- `subscription_role_assignments`: List of subscription scopes where Owner role was assigned

## Troubleshooting

### Permission Issues
Ensure the account running Terraform has:
- Azure AD Global Administrator or Privileged Role Administrator role
- Owner or User Access Administrator role on target subscriptions

### Provider Issues
If you encounter provider issues, run:
```bash
terraform init -upgrade
```

### State Lock Issues
If state file is locked, wait for other operations to complete or use:
```bash
terraform force-unlock <lock-id>
```
