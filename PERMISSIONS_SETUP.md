# Permissions Setup Guide

## Current Issues and Solutions

### Issue 1: Azure AD Directory Role Assignment
**Error:** `Groups without IsAssignableToRole property set cannot be assigned to roles`

**Status:** ✅ **FIXED** - Added `assignable_to_role = true` to Azure AD group

**Next Steps:** 
- Directory role assignment is temporarily disabled
- To enable Global Administrator role assignment, you need **Privileged Role Administrator** permissions

### Issue 2: Policy Definition Creation
**Error:** `AuthorizationFailed` - No permission to create policy definitions

**Status:** ⚠️ **REQUIRES ACTION**

**Required Permissions:**
You need one of these roles at the **Management Group level**:
- **Owner** 
- **Policy Contributor**
- **Resource Policy Contributor**

## How to Fix Permissions

### Option 1: Get Owner Role at Management Group
```bash
# Get your user object ID
az ad signed-in-user show --query id -o tsv

# Assign Owner role at management group level (requires existing Owner)
az role assignment create \
  --assignee <your-user-object-id> \
  --role "Owner" \
  --scope "/providers/Microsoft.Management/managementGroups/myorg"
```

### Option 2: Get Policy Contributor Role
```bash
# Assign Policy Contributor role
az role assignment create \
  --assignee <your-user-object-id> \
  --role "Resource Policy Contributor" \
  --scope "/providers/Microsoft.Management/managementGroups/myorg"
```

### Option 3: Use Service Principal with Proper Permissions
```bash
# Create service principal
az ad sp create-for-rbac --name "terraform-sp" --role Owner \
  --scopes "/providers/Microsoft.Management/managementGroups/myorg"

# Use service principal for Terraform
export ARM_CLIENT_ID="<client-id>"
export ARM_CLIENT_SECRET="<client-secret>"
export ARM_TENANT_ID="<tenant-id>"
export ARM_SUBSCRIPTION_ID="<subscription-id>"
```

## Current Deployment Status

### ✅ What Will Work Now:
- Management Groups creation (Productions, Non Productions)
- Azure AD Group creation with proper role assignment capability
- Built-in policy assignments (Deny-Resource-Locations, Deny-RSG-Locations)
- Azure RBAC role assignments (Owner role at subscription level)

### ⚠️ What's Temporarily Disabled:
- Custom policy definitions (requires Policy Contributor permissions)
- Azure AD Directory role assignments (requires Privileged Role Administrator)

## Deployment Steps

### Step 1: Deploy with Current Permissions
```bash
terraform plan
terraform apply
```

### Step 2: After Getting Proper Permissions

1. **Enable Custom Policy Definitions:**
   - Edit `lib/lib/archetype_definition_productions.json`
   - Add back the policy definitions:
   ```json
   "policy_definitions": [
     "block_public_ip_assignment_to_vms",
     "enforce_network_security_groups",
     "location",
     "require_mandatory_tags",
     "secure_storage_accounts"
   ]
   ```

2. **Enable Directory Role Assignment:**
   - Edit `azure_ad_groups.tf`
   - Change `count = 0` to `count = var.create_infras_team_group ? 1 : 0`

3. **Re-deploy:**
   ```bash
   terraform plan
   terraform apply
   ```

## Verification Commands

### Check Current Permissions:
```bash
# Check your current roles
az role assignment list --assignee $(az ad signed-in-user show --query id -o tsv) --all

# Check management group access
az account management-group show --name myorg
```

### Check Azure AD Permissions:
```bash
# Check your Azure AD roles
az rest --method GET --url "https://graph.microsoft.com/v1.0/me/memberOf" \
  --query "value[?@.displayName=='Global Administrator' || @.displayName=='Privileged Role Administrator']"
```

## Summary

The deployment has been modified to work with current permissions:
- ✅ Management Groups: Productions and Non Productions
- ✅ Azure AD Group: "Infras Team - Super Admin" (with role assignment capability)
- ✅ Azure RBAC: Owner role at subscription level
- ⚠️ Custom Policies: Disabled until proper permissions obtained
- ⚠️ Directory Roles: Disabled until Privileged Role Administrator obtained

Once you get the required permissions, you can enable the disabled features and complete the full deployment.
