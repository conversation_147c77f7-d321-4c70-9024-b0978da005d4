# Azure AD Groups Module Documentation

## Overview

The Azure AD Groups module has been created to provide a reusable, flexible way to manage Azure AD groups and their role assignments. This module replaces the previous inline Azure AD group configuration.

## Module Structure

```
modules/azure_ad_groups/
├── main.tf          # Main module logic
├── variables.tf     # Input variables
├── outputs.tf       # Output values
└── README.md        # Module documentation
```

## Key Features

### ✅ **Flexible Group Management**
- Create multiple Azure AD groups with different configurations
- Support for security groups, mail-enabled groups, and role-assignable groups
- Automatic owner and member management

### ✅ **Role Assignment Management**
- Azure RBAC roles at subscription level
- Azure AD directory roles (with proper permissions)
- Support for single subscription or all subscriptions

### ✅ **Safety Features**
- Lifecycle protection to prevent accidental deletion
- Input validation for group names
- Conditional role assignments based on permissions

## Usage Examples

### Basic Usage (Current Configuration)

```hcl
# In terraform.tfvars
azure_ad_groups = {
  infras_team_super_admin = {
    display_name         = "Infras Team - Super Admin"
    description          = "Infrastructure Team with Super Admin privileges"
    security_enabled     = true
    assignable_to_role   = true
    azure_roles          = ["Owner"]
    directory_roles      = ["Global Administrator"]
    members = [
      "user1-object-id",
      "user2-object-id"
    ]
  }
}

assign_roles_to_all_subscriptions = false
enable_directory_role_assignments = false  # Set to true when you have permissions
```

### Advanced Multi-Group Configuration

```hcl
azure_ad_groups = {
  infras_team_super_admin = {
    display_name         = "Infras Team - Super Admin"
    description          = "Infrastructure Team with Super Admin privileges"
    security_enabled     = true
    assignable_to_role   = true
    azure_roles          = ["Owner", "User Access Administrator"]
    directory_roles      = ["Global Administrator"]
    members              = ["admin1-id", "admin2-id"]
    additional_owners    = ["manager-id"]
  }
  
  developers = {
    display_name         = "Developers"
    description          = "Development team with contributor access"
    security_enabled     = true
    assignable_to_role   = false
    azure_roles          = ["Contributor"]
    members              = ["dev1-id", "dev2-id", "dev3-id"]
  }
  
  security_team = {
    display_name         = "Security Team"
    description          = "Security team with security admin privileges"
    security_enabled     = true
    assignable_to_role   = true
    azure_roles          = ["Security Admin"]
    directory_roles      = ["Security Administrator"]
    members              = ["sec1-id", "sec2-id"]
  }
  
  readers = {
    display_name         = "Read Only Users"
    description          = "Users with read-only access"
    security_enabled     = true
    assignable_to_role   = false
    azure_roles          = ["Reader"]
    members              = ["user1-id", "user2-id", "user3-id"]
  }
}
```

## Migration from Old Configuration

### Before (azure_ad_groups.tf)
```hcl
resource "azuread_group" "infras_team_super_admin" {
  count = var.create_infras_team_group ? 1 : 0
  
  display_name         = "Infras Team - Super Admin"
  description          = "Infrastructure Team..."
  security_enabled     = true
  assignable_to_role   = true
  
  owners  = [data.azuread_client_config.current.object_id]
  members = var.infras_team_group_members
}
```

### After (Module)
```hcl
module "azure_ad_groups" {
  source = "./modules/groups"

  groups = var.azure_ad_groups
  assign_roles_to_all_subscriptions = var.assign_roles_to_all_subscriptions
  enable_directory_role_assignments = var.enable_directory_role_assignments
}
```

## Benefits of Module Approach

### 🎯 **Reusability**
- Can be used in multiple environments
- Easy to share across projects
- Standardized group management

### 🔧 **Maintainability**
- Centralized logic in one place
- Easier to update and improve
- Clear separation of concerns

### 📈 **Scalability**
- Support for unlimited number of groups
- Flexible role assignment patterns
- Easy to add new features

### 🛡️ **Safety**
- Built-in validation
- Lifecycle protection
- Conditional deployments

## Common Role Configurations

### Infrastructure Team
```hcl
infras_team = {
  display_name         = "Infrastructure Team"
  description          = "Infrastructure management team"
  security_enabled     = true
  assignable_to_role   = true
  azure_roles          = ["Owner", "User Access Administrator"]
  directory_roles      = ["Global Administrator"]
}
```

### Development Team
```hcl
developers = {
  display_name         = "Developers"
  description          = "Application development team"
  security_enabled     = true
  assignable_to_role   = false
  azure_roles          = ["Contributor"]
}
```

### Security Team
```hcl
security_team = {
  display_name         = "Security Team"
  description          = "Security and compliance team"
  security_enabled     = true
  assignable_to_role   = true
  azure_roles          = ["Security Admin"]
  directory_roles      = ["Security Administrator"]
}
```

## Deployment Steps

1. **Update terraform.tfvars**:
   ```bash
   cp terraform.tfvars.example terraform.tfvars
   # Edit terraform.tfvars with your group configurations
   ```

2. **Initialize module**:
   ```bash
   terraform init
   ```

3. **Plan deployment**:
   ```bash
   terraform plan
   ```

4. **Apply changes**:
   ```bash
   terraform apply
   ```

## Troubleshooting

### Permission Issues
- Ensure you have proper Azure AD permissions for directory roles
- Use `enable_directory_role_assignments = false` if you don't have Privileged Role Administrator

### Module Not Found
- Ensure the module path is correct: `./modules/azure_ad_groups`
- Run `terraform init` to initialize the module

### Group Already Exists
- The module will import existing groups if they match the configuration
- Use `terraform import` if needed

## Next Steps

1. **Test the module** with basic configuration
2. **Add more groups** as needed for your organization
3. **Enable directory role assignments** when you have proper permissions
4. **Customize role assignments** based on your security requirements

The module is now ready for production use and can be easily extended for future requirements!
