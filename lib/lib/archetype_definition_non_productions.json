{"non_productions": {"policy_assignments": ["Deny-Resource-Locations", "Deny-RSG-Locations", "Require-Mandatory-Tags", "Secure-Storage-Accounts", "Enforce-Network-Security-Groups", "Block-Public-IP-Assignment-To-VMs"], "policy_definitions": ["location", "require_mandatory_tags", "secure_storage_accounts", "enforce_network_security_groups", "block_public_ip_assignment_to_vms"], "policy_set_definitions": [], "role_definitions": [], "archetype_config": {"parameters": {"Deny-Resource-Locations": {"listOfAllowedLocations": ["eastasia", "southeastasia", "eastus", "eastus2", "west<PERSON>", "westus2"]}, "Deny-RSG-Locations": {"listOfAllowedLocations": ["eastasia", "southeastasia", "eastus", "eastus2", "west<PERSON>", "westus2"]}, "Require-Mandatory-Tags": {"Owner": "Owner", "org": "Organization", "create_by": "CreatedBy", "operation_team": "OperationTeam", "project_name": "ProjectName", "env": "Environment", "app_name": "ApplicationName", "resources_type": "ResourceType", "priority": "Priority", "data_zone": "DataZone"}, "Secure-Storage-Accounts": {"effect": "Audit"}, "Enforce-Network-Security-Groups": {"effect": "Audit"}, "Block-Public-IP-Assignment-To-VMs": {"effect": "Audit"}}, "access_control": {}}}}