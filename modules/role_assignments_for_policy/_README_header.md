# Role Assignment for Policy sub-module

> [!IMPORTANT]
> For new deployments we now recommend using Azure Verified Modules for Platform Landing Zones.
> Please see the documentation at <https://aka.ms/alz/tf>.

## ⚠️ DEPRECATION NOTICE

**This module is now in extended support mode and will be archived on August 1, 2026.**

### Current Status

- **Extended Support Period**: This module is now in extended support for one year (until August 1, 2026)
- **Support Scope**: During this period, we will provide quality updates (e.g. bug fixes) and policy library updates only
- **No New Features**: No new features or functionality will be added to this module

### Migration Path

We strongly recommend that all users migrate to the new **Azure Verified Modules** approach for Azure Landing Zones. This new approach provides:

- Enhanced reliability and testing
- Improved modularity and flexibility
- Better alignment with Azure best practices
- Ongoing feature development and support

**Further reading**: Please read our recent [blog](https://techcommunity.microsoft.com/blog/azuretoolsblog/terraform-azure-verified-modules-for-platform-landing-zone-alz-migration-guidanc/4432035)

**Migration Guide**: Please visit [aka.ms/alz/tf/migrate](https://aka.ms/alz/tf/migrate) for detailed migration guidance and resources.

### Timeline

- **Now - August 1, 2026**: Extended support (quality and policy updates only)
- **August 1, 2026**: Repository will be archived and no further updates will be made

### Questions?

If you have questions about the migration process or need assistance, please refer to the migration documentation or raise an issue in the repository before the archive date.
