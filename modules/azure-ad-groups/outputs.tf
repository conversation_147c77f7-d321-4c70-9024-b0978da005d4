output "groups" {
  description = "Information about created Azure AD groups"
  value = {
    for key, group in azuread_group.groups : key => {
      object_id    = group.object_id
      display_name = group.display_name
      description  = group.description
    }
  }
}

output "subscription_role_assignments" {
  description = "List of subscription role assignments"
  value = concat(
    [for assignment in azurerm_role_assignment.subscription_roles : assignment.scope],
    [for assignment in azurerm_role_assignment.all_subscriptions_roles : assignment.scope]
  )
}

output "azure_ad_role_assignments" {
  description = "List of Azure AD directory role assignments"
  value = [
    for assignment in azuread_directory_role_assignment.azure_ad_roles : {
      group_key = assignment.principal_object_id
      role_id   = assignment.role_id
    }
  ]
} 