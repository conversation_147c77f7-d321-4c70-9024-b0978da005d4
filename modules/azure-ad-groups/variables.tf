variable "groups" {
  description = "Map of Azure AD groups to create"
  type = map(object({
    display_name     = string
    description      = string
    security_enabled = optional(bool, true)
    owners           = optional(list(string), [])
    members          = optional(list(string), [])
  }))
}

variable "role_assignments" {
  description = "List of Azure RBAC role assignments for groups"
  type = list(object({
    group_key   = string
    role_name   = string
    scope       = string
    scope_type  = string # "subscription" or "all_subscriptions"
  }))
  default = []
}

variable "azure_ad_role_assignments" {
  description = "List of Azure AD directory role assignments for groups"
  type = list(object({
    group_key  = string
    role_name  = string
  }))
  default = []
}

variable "assign_roles_to_all_subscriptions" {
  description = "If set to true, will assign roles to all available subscriptions"
  type        = bool
  default     = false
} 