# Azure AD Groups Module

Module này tạo các Azure AD Groups và gán các role cần thiết.

## Usage

```hcl
module "azure_ad_groups" {
  source = "./modules/azure-ad-groups"

  groups = {
    infras_team_super_admin = {
      display_name     = "Infras Team - Super Admin"
      description      = "Infrastructure Team with Super Admin privileges"
      security_enabled = true
      members          = ["user1-object-id", "user2-object-id"]
      owners           = ["owner1-object-id"]
    }
    
    dev_team = {
      display_name     = "Development Team"
      description      = "Development team with Contributor access"
      security_enabled = true
      members          = ["dev1-object-id", "dev2-object-id"]
    }
  }

  role_assignments = [
    {
      group_key  = "infras_team_super_admin"
      role_name  = "Owner"
      scope      = "current"
      scope_type = "subscription"
    },
    {
      group_key  = "dev_team"
      role_name  = "Contributor"
      scope      = "current"
      scope_type = "subscription"
    }
  ]

  azure_ad_role_assignments = [
    {
      group_key = "infras_team_super_admin"
      role_name = "Global Administrator"
    }
  ]

  assign_roles_to_all_subscriptions = false
}
```

## Inputs

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|:--------:|
| groups | Map of Azure AD groups to create | `map(object)` | n/a | yes |
| role_assignments | List of Azure RBAC role assignments for groups | `list(object)` | `[]` | no |
| azure_ad_role_assignments | List of Azure AD directory role assignments for groups | `list(object)` | `[]` | no |
| assign_roles_to_all_subscriptions | If set to true, will assign roles to all available subscriptions | `bool` | `false` | no |

## Outputs

| Name | Description |
|------|-------------|
| groups | Information about created Azure AD groups |
| subscription_role_assignments | List of subscription role assignments |
| azure_ad_role_assignments | List of Azure AD directory role assignments |

## Group Object Structure

```hcl
object({
  display_name     = string
  description      = string
  security_enabled = optional(bool, true)
  owners           = optional(list(string), [])
  members          = optional(list(string), [])
})
```

## Role Assignment Object Structure

```hcl
object({
  group_key  = string
  role_name  = string
  scope      = string
  scope_type = string # "subscription" or "all_subscriptions"
})
```

## Azure AD Role Assignment Object Structure

```hcl
object({
  group_key = string
  role_name = string
})
``` 