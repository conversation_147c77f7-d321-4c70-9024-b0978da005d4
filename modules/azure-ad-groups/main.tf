# Get current client configuration to retrieve tenant information
data "azuread_client_config" "current" {}

# Get current subscription
data "azurerm_client_config" "current" {}

# Get all subscriptions to assign roles at subscription level (only if needed)
data "azurerm_subscriptions" "available" {
  count = var.assign_roles_to_all_subscriptions ? 1 : 0
}

# Create Azure AD Groups
resource "azuread_group" "groups" {
  for_each = var.groups

  display_name     = each.value.display_name
  description      = each.value.description
  security_enabled = lookup(each.value, "security_enabled", true)

  # Add owners to the group
  owners = concat(
    [data.azuread_client_config.current.object_id],
    lookup(each.value, "owners", [])
  )

  # Add members to the group if specified
  members = lookup(each.value, "members", [])
}

# Assign Azure RBAC roles to groups
resource "azurerm_role_assignment" "subscription_roles" {
  for_each = {
    for assignment in var.role_assignments : "${assignment.group_key}.${assignment.role_name}.${assignment.scope}" => assignment
    if assignment.scope_type == "subscription"
  }

  scope                = each.value.scope == "current" ? "/subscriptions/${data.azurerm_client_config.current.subscription_id}" : each.value.scope
  role_definition_name = each.value.role_name
  principal_id         = azuread_group.groups[each.value.group_key].object_id
}

# Assign roles to all subscriptions if specified
resource "azurerm_role_assignment" "all_subscriptions_roles" {
  for_each = {
    for assignment in var.role_assignments : "${assignment.group_key}.${assignment.role_name}.all_subs" => assignment
    if assignment.scope_type == "all_subscriptions" && var.assign_roles_to_all_subscriptions
  }

  scope                = "/subscriptions/${data.azurerm_subscriptions.available[0].subscriptions[each.key].subscription_id}"
  role_definition_name = each.value.role_name
  principal_id         = azuread_group.groups[each.value.group_key].object_id
}

# Get Azure AD directory roles
data "azuread_directory_roles" "available" {
  count = length(var.azure_ad_role_assignments) > 0 ? 1 : 0
}

# Assign Azure AD directory roles to groups
resource "azuread_directory_role_assignment" "azure_ad_roles" {
  for_each = {
    for assignment in var.azure_ad_role_assignments : "${assignment.group_key}.${assignment.role_name}" => assignment
  }

  role_id             = [
    for role in data.azuread_directory_roles.available[0].roles :
    role.object_id if role.display_name == each.value.role_name
  ][0]
  principal_object_id = azuread_group.groups[each.value.group_key].object_id
} 