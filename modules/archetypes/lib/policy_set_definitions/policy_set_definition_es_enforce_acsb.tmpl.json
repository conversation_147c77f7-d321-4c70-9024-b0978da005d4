{"name": "Enforce-ACSB", "type": "Microsoft.Authorization/policySetDefinitions", "apiVersion": "2021-06-01", "scope": null, "properties": {"policyType": "Custom", "displayName": "Enforce Azure Compute Security Benchmark compliance auditing", "description": "Enforce Azure Compute Security Benchmark compliance auditing for Windows and Linux virtual machines.", "metadata": {"version": "1.1.0", "category": "Guest Configuration", "source": "https://github.com/Azure/Enterprise-Scale/", "alzCloudEnvironments": ["AzureCloud"]}, "parameters": {"includeArcMachines": {"type": "String", "allowedValues": ["true", "false"], "metadata": {"displayName": "Include Arc connected servers", "description": "By selecting this option, you agree to be charged monthly per Arc connected machine."}, "defaultValue": "true"}, "effect": {"type": "String", "metadata": {"displayName": "Effect", "description": "Enable or disable the execution of the policy"}, "allowedValues": ["AuditIfNotExists", "Disabled"], "defaultValue": "AuditIfNotExists"}}, "policyDefinitions": [{"policyDefinitionReferenceId": "GcIdentity", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/3cf2ab00-13f1-4d0c-8971-2ac904541a7e", "parameters": {}, "groupNames": [], "definitionVersion": "4.*.*"}, {"policyDefinitionReferenceId": "GcLinux", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/331e8ea8-378a-410f-a2e5-ae22f38bb0da", "parameters": {}, "groupNames": [], "definitionVersion": "3.*.*"}, {"policyDefinitionReferenceId": "GcWindows", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/385f5831-96d4-41db-9a3c-cd3af78aaae6", "parameters": {}, "groupNames": [], "definitionVersion": "1.*.*"}, {"policyDefinitionReferenceId": "WinAcsb", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/72650e9f-97bc-4b2a-ab5f-9781a9fcecbc", "parameters": {"effect": {"value": "[parameters('effect')]"}, "IncludeArcMachines": {"value": "[parameters('includeArcMachines')]"}}, "groupNames": [], "definitionVersion": "2.*.*"}, {"policyDefinitionReferenceId": "LinAcsb", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/fc9b3da7-8347-4380-8e70-0a0361d8dedd", "parameters": {"effect": {"value": "[parameters('effect')]"}, "IncludeArcMachines": {"value": "[parameters('includeArcMachines')]"}}, "groupNames": [], "definitionVersion": "2.*.*"}], "policyDefinitionGroups": null}}