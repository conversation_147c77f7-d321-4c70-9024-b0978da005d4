{"name": "Enforce-Guardrails-VirtualDesktop", "type": "Microsoft.Authorization/policySetDefinitions", "apiVersion": "2021-06-01", "scope": null, "properties": {"policyType": "Custom", "displayName": "Enforce recommended guardrails for Virtual Desktop", "description": "This policy initiative is a group of policies that ensures Virtual Desktop is compliant per regulated Landing Zones.", "metadata": {"version": "1.1.0", "category": "Desktop Virtualization", "source": "https://github.com/Azure/Enterprise-Scale/", "alzCloudEnvironments": ["AzureCloud", "AzureChinaCloud", "AzureUSGovernment"]}, "parameters": {"avdWorkspaceModifyPublicNetworkAccess": {"type": "string", "defaultValue": "Modify", "allowedValues": ["Modify", "Disabled"]}, "avdHostPoolModifyPublicNetworkAccess": {"type": "string", "defaultValue": "Modify", "allowedValues": ["Modify", "Disabled"]}}, "policyDefinitions": [{"policyDefinitionReferenceId": "Modify-Workspace-PublicNetworkAccess", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/ce6ebf1d-0b94-4df9-9257-d8cacc238b4f", "parameters": {"effect": {"value": "[parameters('avdWorkspaceModifyPublicNetworkAccess')]"}}, "groupNames": [], "definitionVersion": "1.*.*"}, {"policyDefinitionReferenceId": "Modify-Hostpool-PublicNetworkAccess", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/2a0913ff-51e7-47b8-97bb-ea17127f7c8d", "parameters": {"effect": {"value": "[parameters('avdHostPoolModifyPublicNetworkAccess')]"}}, "groupNames": [], "definitionVersion": "1.*.*"}], "policyDefinitionGroups": null}}