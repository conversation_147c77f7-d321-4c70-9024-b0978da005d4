{"name": "Deploy-Sql-Security", "type": "Microsoft.Authorization/policySetDefinitions", "apiVersion": "2021-06-01", "scope": null, "properties": {"policyType": "Custom", "displayName": "[Deprecated]: Deploy SQL Database built-in SQL security configuration", "description": "Deploy auditing, Alert, TDE and SQL vulnerability to SQL Databases when it not exist in the deployment. Superseded by https://www.azadvertizer.net/azpolicyinitiativesadvertizer/Deploy-Sql-Security_20240529.html", "metadata": {"version": "1.0.0-deprecated", "category": "SQL", "source": "https://github.com/Azure/Enterprise-Scale/", "deprecated": true, "supersededBy": "Deploy-Sql-Security_20240529", "alzCloudEnvironments": ["AzureCloud", "AzureChinaCloud", "AzureUSGovernment"]}, "parameters": {"vulnerabilityAssessmentsEmail": {"metadata": {"description": "The email address to send alerts", "displayName": "The email address to send alerts"}, "type": "String"}, "vulnerabilityAssessmentsStorageID": {"metadata": {"description": "The storage account ID to store assessments", "displayName": "The storage account ID to store assessments"}, "type": "String"}, "SqlDbTdeDeploySqlSecurityEffect": {"type": "String", "defaultValue": "DeployIfNotExists", "allowedValues": ["DeployIfNotExists", "Disabled"], "metadata": {"displayName": "Deploy SQL Database Transparent Data Encryption ", "description": "Deploy the Transparent Data Encryption when it is not enabled in the deployment"}}, "SqlDbSecurityAlertPoliciesDeploySqlSecurityEffect": {"type": "String", "defaultValue": "DeployIfNotExists", "allowedValues": ["DeployIfNotExists", "Disabled"], "metadata": {"displayName": "Deploy SQL Database security Alert Policies configuration with email admin accounts", "description": "Deploy the security Alert Policies configuration with email admin accounts when it not exist in current configuration"}}, "SqlDbAuditingSettingsDeploySqlSecurityEffect": {"type": "String", "defaultValue": "DeployIfNotExists", "allowedValues": ["DeployIfNotExists", "Disabled"], "metadata": {"displayName": "Deploy SQL database auditing settings", "description": "Deploy auditing settings to SQL Database when it not exist in the deployment"}}, "SqlDbVulnerabilityAssessmentsDeploySqlSecurityEffect": {"type": "String", "defaultValue": "DeployIfNotExists", "allowedValues": ["DeployIfNotExists", "Disabled"], "metadata": {"displayName": "Deploy SQL Database vulnerability Assessments", "description": "Deploy SQL Database vulnerability Assessments when it not exist in the deployment. To the specific  storage account in the parameters"}}}, "policyDefinitions": [{"policyDefinitionReferenceId": "SqlDbTdeDeploySqlSecurity", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/86a912f6-9a06-4e26-b447-11b16ba8659f", "parameters": {"effect": {"value": "[parameters('SqlDbTdeDeploySqlSecurityEffect')]"}}, "groupNames": [], "definitionVersion": ""}, {"policyDefinitionReferenceId": "SqlDbSecurityAlertPoliciesDeploySqlSecurity", "policyDefinitionId": "${root_scope_resource_id}/providers/Microsoft.Authorization/policyDefinitions/Deploy-Sql-SecurityAlertPolicies", "parameters": {"effect": {"value": "[parameters('SqlDbSecurityAlertPoliciesDeploySqlSecurityEffect')]"}}, "groupNames": [], "definitionVersion": ""}, {"policyDefinitionReferenceId": "SqlDbAuditingSettingsDeploySqlSecurity", "policyDefinitionId": "${root_scope_resource_id}/providers/Microsoft.Authorization/policyDefinitions/Deploy-Sql-AuditingSettings", "parameters": {"effect": {"value": "[parameters('SqlDbAuditingSettingsDeploySqlSecurityEffect')]"}}, "groupNames": [], "definitionVersion": ""}, {"policyDefinitionReferenceId": "SqlDbVulnerabilityAssessmentsDeploySqlSecurity", "policyDefinitionId": "${root_scope_resource_id}/providers/Microsoft.Authorization/policyDefinitions/Deploy-Sql-vulnerabilityAssessments", "parameters": {"effect": {"value": "[parameters('SqlDbVulnerabilityAssessmentsDeploySqlSecurityEffect')]"}, "vulnerabilityAssessmentsEmail": {"value": "[parameters('vulnerabilityAssessmentsEmail')]"}, "vulnerabilityAssessmentsStorageID": {"value": "[parameters('vulnerabilityAssessmentsStorageID')]"}}, "groupNames": [], "definitionVersion": ""}], "policyDefinitionGroups": null}}