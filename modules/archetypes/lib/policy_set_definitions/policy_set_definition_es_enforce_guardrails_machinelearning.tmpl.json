{"name": "Enforce-Guardrails-MachineLearning", "type": "Microsoft.Authorization/policySetDefinitions", "apiVersion": "2021-06-01", "scope": null, "properties": {"policyType": "Custom", "displayName": "Enforce recommended guardrails for Machine Learning", "description": "This policy initiative is a group of policies that ensures Machine Learning is compliant per regulated Landing Zones.", "metadata": {"version": "1.2.0", "category": "Machine Learning", "source": "https://github.com/Azure/Enterprise-Scale/", "alzCloudEnvironments": ["AzureCloud", "AzureChinaCloud", "AzureUSGovernment"]}, "parameters": {"mlUserAssignedIdentity": {"type": "string", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"]}, "mlModifyLocalAuth": {"type": "string", "defaultValue": "Modify", "allowedValues": ["Modify", "Disabled"]}, "mlLocalAuth": {"type": "string", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"]}, "mlOutdatedOS": {"type": "string", "defaultValue": "Audit", "allowedValues": ["Audit", "Disabled"]}, "mlModifyPublicNetworkAccess": {"type": "string", "defaultValue": "Modify", "allowedValues": ["Modify", "Disabled"]}, "mlIdleShutdown": {"type": "string", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"]}, "mlVirtualNetwork": {"type": "string", "defaultValue": "Audit", "allowedValues": ["Audit", "Disabled"]}, "mlLegacyMode": {"type": "string", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"]}, "mlPrivateLink": {"type": "string", "defaultValue": "Audit", "allowedValues": ["Audit", "Disabled"]}, "mlResourceLogs": {"type": "string", "defaultValue": "AuditIfNotExists", "allowedValues": ["AuditIfNotExists", "Disabled"]}, "mlAllowedRegistryDeploy": {"type": "string", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["<PERSON><PERSON>", "Disabled"]}, "mlAllowedModule": {"type": "string", "defaultValue": "enforceSetting", "allowedValues": ["enforceSetting", "disabled"]}, "mlAllowedPython": {"type": "string", "defaultValue": "enforceSetting", "allowedValues": ["enforceSetting", "disabled"]}, "mlAllowedRegistries": {"type": "string", "defaultValue": "enforceSetting", "allowedValues": ["enforceSetting", "disabled"]}}, "policyDefinitions": [{"policyDefinitionReferenceId": "Deny-ML-Outdated-Os", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/f110a506-2dcb-422e-bcea-d533fc8c35e2", "parameters": {"effects": {"value": "[parameters('mlOutdatedOS')]"}}, "groupNames": [], "definitionVersion": "1.*.*"}, {"policyDefinitionReferenceId": "Deny-ML-Local-Auth", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/e96a9a5f-07ca-471b-9bc5-6a0f33cbd68f", "parameters": {"effect": {"value": "[parameters('mlLocalAuth')]"}}, "groupNames": [], "definitionVersion": "2.*.*"}, {"policyDefinitionReferenceId": "Modify-ML-Local-Auth", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/a6f9a2d0-cff7-4855-83ad-4cd750666512", "parameters": {"effect": {"value": "[parameters('mlModifyLocalAuth')]"}}, "groupNames": [], "definitionVersion": "2.*.*"}, {"policyDefinitionReferenceId": "Deny-ML-User-Assigned-Identity", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/5f0c7d88-c7de-45b8-ac49-db49e72eaa78", "parameters": {"effect": {"value": "[parameters('mlUserAssignedIdentity')]"}}, "groupNames": [], "definitionVersion": "1.*.*"}, {"policyDefinitionReferenceId": "Modify-ML-Public-Network-Access", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/a10ee784-7409-4941-b091-663697637c0f", "parameters": {"effect": {"value": "[parameters('mlModifyPublicNetworkAccess')]"}}, "groupNames": [], "definitionVersion": "1.*.*"}, {"policyDefinitionReferenceId": "<PERSON>y-<PERSON><PERSON>-<PERSON>dle-Shutdown", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/679ddf89-ab8f-48a5-9029-e76054077449", "parameters": {"effect": {"value": "[parameters('mlIdleShutdown')]"}}, "groupNames": [], "definitionVersion": "1.*.*"}, {"policyDefinitionReferenceId": "Audit-ML-Virtual-Network", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/7804b5c7-01dc-4723-969b-ae300cc07ff1", "parameters": {"effect": {"value": "[parameters('mlVirtualNetwork')]"}}, "groupNames": [], "definitionVersion": "1.*.*"}, {"policyDefinitionReferenceId": "Deny-ML-Legacy-Mode", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/e413671a-dd10-4cc1-a943-45b598596cb7", "parameters": {"effect": {"value": "[parameters('mlLegacyMode')]"}}, "groupNames": [], "definitionVersion": "1.*.*"}, {"policyDefinitionReferenceId": "Audit-ML-Private-Link", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/45e05259-1eb5-4f70-9574-baf73e9d219b", "parameters": {"effect": {"value": "[parameters('mlPrivateLink')]"}}, "groupNames": [], "definitionVersion": "1.*.*"}, {"policyDefinitionReferenceId": "Aine-ML-Resource-Logs", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/afe0c3be-ba3b-4544-ba52-0c99672a8ad6", "parameters": {"effect": {"value": "[parameters('mlResourceLogs')]"}}, "groupNames": [], "definitionVersion": "1.*.*"}, {"policyDefinitionReferenceId": "Deny-<PERSON><PERSON>-Allowed-Regis<PERSON>-Deploy", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/19539b54-c61e-4196-9a38-67598701be90", "parameters": {"effect": {"value": "[parameters('mlAllowedRegistryDeploy')]"}}, "groupNames": [], "definitionVersion": "1.*.*-preview"}, {"policyDefinitionReferenceId": "<PERSON><PERSON>-<PERSON><PERSON>-<PERSON><PERSON>-<PERSON><PERSON><PERSON>", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/53c70b02-63dd-11ea-bc55-0242ac130003", "parameters": {"effect": {"value": "[parameters('mlAllowedModule')]"}}, "groupNames": [], "definitionVersion": "6.*.*-preview"}, {"policyDefinitionReferenceId": "<PERSON><PERSON><PERSON><PERSON><PERSON>-Allowed-<PERSON>", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/77eeea86-7e81-4a7d-9067-de844d096752", "parameters": {"effect": {"value": "[parameters('mlAllowedPython')]"}}, "groupNames": [], "definitionVersion": "5.*.*-preview"}, {"policyDefinitionReferenceId": "Deny-<PERSON><PERSON>-Allowed-Registries", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/5853517a-63de-11ea-bc55-0242ac130003", "parameters": {"effect": {"value": "[parameters('mlAllowedRegistries')]"}}, "groupNames": [], "definitionVersion": "6.*.*-preview"}], "policyDefinitionGroups": null}}