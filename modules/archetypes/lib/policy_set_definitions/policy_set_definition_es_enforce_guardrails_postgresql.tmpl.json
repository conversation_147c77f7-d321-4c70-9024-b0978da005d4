{"name": "Enforce-Guardrails-PostgreSQL", "type": "Microsoft.Authorization/policySetDefinitions", "apiVersion": "2021-06-01", "scope": null, "properties": {"policyType": "Custom", "displayName": "Enforce recommended guardrails for PostgreSQL", "description": "This policy initiative is a group of policies that ensures PostgreSQL is compliant per regulated Landing Zones.", "metadata": {"version": "1.1.0", "category": "PostgreSQL", "source": "https://github.com/Azure/Enterprise-Scale/", "alzCloudEnvironments": ["AzureCloud", "AzureChinaCloud", "AzureUSGovernment"]}, "parameters": {"postgreSqlAdvThreatProtection": {"type": "string", "defaultValue": "DeployIfNotExists", "allowedValues": ["DeployIfNotExists", "Disabled"]}}, "policyDefinitions": [{"policyDefinitionReferenceId": "Dine-PostgreSql-Adv-Threat-Protection", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/db048e65-913c-49f9-bb5f-1084184671d3", "parameters": {"effect": {"value": "[parameters('postgreSqlAdvThreatProtection')]"}}, "groupNames": [], "definitionVersion": "1.*.*"}], "policyDefinitionGroups": null}}