{"name": "Enforce-ALZ-Decomm", "type": "Microsoft.Authorization/policySetDefinitions", "apiVersion": "2021-06-01", "scope": null, "properties": {"policyType": "Custom", "displayName": "Enforce policies in the Decommissioned Landing Zone", "description": "Enforce policies in the Decommissioned Landing Zone.", "metadata": {"version": "1.1.0", "category": "Decommissioned", "source": "https://github.com/Azure/Enterprise-Scale/", "alzCloudEnvironments": ["AzureCloud", "AzureChinaCloud", "AzureUSGovernment"]}, "parameters": {"listOfResourceTypesAllowed": {"type": "Array", "defaultValue": [], "metadata": {"displayName": "Allowed resource types in the Decommissioned landing zone", "description": "Allowed resource types in the Decommissioned landing zone, default is none.", "strongType": "resourceTypes"}}}, "policyDefinitions": [{"policyDefinitionReferenceId": "DecomDenyResources", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/a08ec900-254a-4555-9bf5-e42af04b5c5c", "parameters": {"listOfResourceTypesAllowed": {"value": "[parameters('listOfResourceTypesAllowed')]"}}, "groupNames": [], "definitionVersion": "1.*.*"}, {"policyDefinitionReferenceId": "DecomShutdownMachines", "policyDefinitionId": "${root_scope_resource_id}/providers/Microsoft.Authorization/policyDefinitions/Deploy-Vm-autoShutdown", "parameters": {}, "groupNames": [], "definitionVersion": "1.*.*"}], "policyDefinitionGroups": null}}