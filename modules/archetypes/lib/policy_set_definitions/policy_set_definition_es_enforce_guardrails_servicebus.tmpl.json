{"name": "Enforce-Guardrails-ServiceBus", "type": "Microsoft.Authorization/policySetDefinitions", "apiVersion": "2021-06-01", "scope": null, "properties": {"policyType": "Custom", "displayName": "Enforce recommended guardrails for Service Bus", "description": "This policy initiative is a group of policies that ensures Service Bus is compliant per regulated Landing Zones.", "metadata": {"version": "1.1.0", "category": "Service Bus", "source": "https://github.com/Azure/Enterprise-Scale/", "alzCloudEnvironments": ["AzureCloud", "AzureChinaCloud", "AzureUSGovernment"]}, "parameters": {"serviceBusModifyDisableLocalAuth": {"type": "string", "defaultValue": "Modify", "allowedValues": ["Modify", "Disabled"]}, "serviceBusDenyDisabledLocalAuth": {"type": "string", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"]}, "serviceBusDoubleEncryption": {"type": "string", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"]}, "serviceBusAuthzRules": {"type": "string", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"]}}, "policyDefinitions": [{"policyDefinitionReferenceId": "Deny-<PERSON>b-Authz-Rules", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/a1817ec0-a368-432a-8057-8371e17ac6ee", "parameters": {"effect": {"value": "[parameters('serviceBusAuthzRules')]"}}, "groupNames": [], "definitionVersion": "1.*.*"}, {"policyDefinitionReferenceId": "Deny-Sb-Encryption", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/ebaf4f25-a4e8-415f-86a8-42d9155bef0b", "parameters": {"effect": {"value": "[parameters('serviceBusDoubleEncryption')]"}}, "groupNames": [], "definitionVersion": "1.*.*"}, {"policyDefinitionReferenceId": "Deny-Sb-LocalAuth", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/cfb11c26-f069-4c14-8e36-56c394dae5af", "parameters": {"effect": {"value": "[parameters('serviceBusDenyDisabledLocalAuth')]"}}, "groupNames": [], "definitionVersion": "1.*.*"}, {"policyDefinitionReferenceId": "Modify-Sb-LocalAuth", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/910711a6-8aa2-4f15-ae62-1e5b2ed3ef9e", "parameters": {"effect": {"value": "[parameters('serviceBusModifyDisableLocalAuth')]"}}, "groupNames": [], "definitionVersion": "1.*.*"}], "policyDefinitionGroups": null}}