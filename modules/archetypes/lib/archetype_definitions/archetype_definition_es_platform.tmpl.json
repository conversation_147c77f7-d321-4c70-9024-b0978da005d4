{"es_platform": {"policy_assignments": ["DenyAction-DeleteUAMIAMA", "Deploy-MDFC-DefSQL-AMA", "Deploy-VM-ChangeTrack", "Deploy-VM-Monitoring", "Deploy-vmArc-ChangeTrack", "Deploy-vmHybr-Monitoring", "Deploy-VMSS-ChangeTrack", "Deploy-VMSS-Monitoring", "Enable-AUM-CheckUpdates", "Enforce-ASR", "Enforce-Encrypt-CMK0", "Enforce-GR-APIM0", "Enforce-GR-AppServices0", "Enforce-GR-Automation0", "Enforce-GR-BotService0", "Enforce-GR-CogServ0", "Enforce-GR-Compute0", "Enforce-GR-ContApps0", "Enforce-GR-ContInst0", "Enforce-GR-ContReg0", "Enforce-GR-CosmosDb0", "Enforce-GR-DataExpl0", "Enforce-GR-DataFactory0", "Enforce-GR-EventGrid0", "Enforce-GR-EventHub0", "Enforce-GR-Key<PERSON>ault", "Enforce-GR-KeyVaultSup0", "Enforce-GR-Kubernetes0", "Enforce-GR-MachLearn0", "Enforce-GR-MySQL0", "Enforce-GR-Network0", "Enforce-GR-OpenAI0", "Enforce-GR-PostgreSQL0", "Enforce-GR-ServiceBus0", "Enforce-GR-SQL0", "Enforce-GR-Storage0", "Enforce-GR-Synapse0", "Enforce-GR-VirtualDesk0", "Enforce-Subnet-Private"], "policy_definitions": [], "policy_set_definitions": [], "role_definitions": [], "archetype_config": {"parameters": {}, "access_control": {}}}}