{"name": "Deploy-Sql-vulnerabilityAssessments_20230706", "type": "Microsoft.Authorization/policyDefinitions", "apiVersion": "2021-06-01", "scope": null, "properties": {"policyType": "Custom", "mode": "Indexed", "displayName": "Deploy SQL Database Vulnerability Assessments", "description": "Deploy SQL Database Vulnerability Assessments when it does not exist in the deployment, and save results to the storage account specified in the parameters.", "metadata": {"version": "1.0.0", "category": "SQL", "source": "https://github.com/Azure/Enterprise-Scale/", "replacesPolicy": "Deploy-Sql-vulnerabilityAssessments", "alzCloudEnvironments": ["AzureCloud", "AzureChinaCloud", "AzureUSGovernment"]}, "parameters": {"vulnerabilityAssessmentsEmail": {"type": "Array", "metadata": {"description": "The email address(es) to send alerts.", "displayName": "The email address(es) to send alerts."}}, "vulnerabilityAssessmentsStorageID": {"type": "String", "metadata": {"description": "The storage account ID to store assessments", "displayName": "The storage account ID to store assessments", "assignPermissions": true}}, "effect": {"type": "String", "defaultValue": "DeployIfNotExists", "allowedValues": ["DeployIfNotExists", "Disabled"], "metadata": {"displayName": "Effect", "description": "Enable or disable the execution of the policy"}}}, "policyRule": {"if": {"field": "type", "equals": "Microsoft.Sql/servers/databases"}, "then": {"effect": "[parameters('effect')]", "details": {"type": "Microsoft.Sql/servers/databases/vulnerabilityAssessments", "existenceCondition": {"allOf": [{"count": {"field": "Microsoft.Sql/servers/databases/vulnerabilityAssessments/recurringScans.emails[*]", "where": {"value": "current(Microsoft.Sql/servers/databases/vulnerabilityAssessments/recurringScans.emails[*])", "notIn": "[parameters('vulnerabilityAssessmentsEmail')]"}}, "greater": 0}, {"field": "Microsoft.Sql/servers/databases/vulnerabilityAssessments/recurringScans.isEnabled", "equals": true}]}, "deployment": {"properties": {"mode": "Incremental", "template": {"$schema": "http://schema.management.azure.com/schemas/2015-01-01/deploymentTemplate.json#", "contentVersion": "*******", "parameters": {"location": {"type": "String"}, "sqlServerName": {"type": "String"}, "sqlServerDataBaseName": {"type": "String"}, "vulnerabilityAssessmentsEmail": {"type": "Array"}, "vulnerabilityAssessmentsStorageID": {"type": "String"}}, "variables": {}, "resources": [{"name": "[concat(parameters('sqlServerName'),'/',parameters('sqlServerDataBaseName'),'/default')]", "type": "Microsoft.Sql/servers/databases/vulnerabilityAssessments", "apiVersion": "2017-03-01-preview", "properties": {"storageContainerPath": "[concat('https://', last( split(parameters('vulnerabilityAssessmentsStorageID') ,  '/') ) , '.blob.core.windows.net/vulneraabilitylogs')]", "storageAccountAccessKey": "[listkeys(parameters('vulnerabilityAssessmentsStorageID'), providers('Microsoft.Storage', 'storageAccounts').apiVersions[0]).keys[0].value]", "recurringScans": {"isEnabled": true, "emailSubscriptionAdmins": false, "emails": "[parameters('vulnerabilityAssessmentsEmail')]"}}}], "outputs": {}}, "parameters": {"location": {"value": "[field('location')]"}, "sqlServerName": {"value": "[first(split(field('fullname'),'/'))]"}, "sqlServerDataBaseName": {"value": "[field('name')]"}, "vulnerabilityAssessmentsEmail": {"value": "[parameters('vulnerabilityAssessmentsEmail')]"}, "vulnerabilityAssessmentsStorageID": {"value": "[parameters('vulnerabilityAssessmentsStorageID')]"}}}}, "roleDefinitionIds": ["/providers/Microsoft.Authorization/roleDefinitions/056cd41c-7e88-42e1-933e-88ba6a50c9c3", "/providers/Microsoft.Authorization/roleDefinitions/749f88d5-cbae-40b8-bcfc-e573ddc772fa", "/providers/Microsoft.Authorization/roleDefinitions/17d1049b-9a84-46fb-8f53-869881c3d3ab"]}}}}}