{"name": "Deny-FileServices-InsecureKerberos", "type": "Microsoft.Authorization/policyDefinitions", "apiVersion": "2021-06-01", "scope": null, "properties": {"policyType": "Custom", "mode": "All", "displayName": "File Services with insecure Kerberos ticket encryption should be denied", "description": "This policy denies the use of insecure Kerberos ticket encryption (RC4-HMAC) when using File Services on a storage account.", "metadata": {"version": "1.0.0", "category": "Storage", "source": "https://github.com/Azure/Enterprise-Scale/", "alzCloudEnvironments": ["AzureCloud", "AzureChinaCloud", "AzureUSGovernment"]}, "parameters": {"effect": {"type": "String", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"], "metadata": {"displayName": "Effect", "description": "The effect determines what happens when the policy rule is evaluated to match"}}, "notAllowedKerberosTicketEncryption": {"type": "String", "defaultValue": "RC4-HMAC", "allowedValues": ["RC4-HMAC", "AES-256"], "metadata": {"displayName": "Kerberos ticket encryption supported by server. Valid values are RC4-HMAC, AES-256.", "description": "The list of kerberosTicketEncryption not allowed."}}}, "policyRule": {"if": {"allOf": [{"field": "type", "equals": "Microsoft.Storage/storageAccounts/fileServices"}, {"field": "Microsoft.Storage/storageAccounts/fileServices/protocolSettings.smb.kerberosTicketEncryption", "contains": "[parameters('notAllowedKerberosTicketEncryption')]"}]}, "then": {"effect": "[parameters('effect')]"}}}}