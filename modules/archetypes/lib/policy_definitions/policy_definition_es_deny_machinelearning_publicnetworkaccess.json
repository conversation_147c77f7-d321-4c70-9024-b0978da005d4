{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>-PublicNetworkAccess", "type": "Microsoft.Authorization/policyDefinitions", "apiVersion": "2021-06-01", "scope": null, "properties": {"policyType": "Custom", "mode": "Indexed", "displayName": "[Deprecated] Azure Machine Learning should have disabled public network access", "description": "Denies public network access for Azure Machine Learning workspaces. Superseded by https://www.azadvertizer.net/azpolicyadvertizer/438c38d2-3772-465a-a9cc-7a6666a275ce.html", "metadata": {"version": "1.0.0-deprecated", "category": "Machine Learning", "source": "https://github.com/Azure/Enterprise-Scale/", "deprecated": true, "supersededBy": "438c38d2-3772-465a-a9cc-7a6666a275ce", "alzCloudEnvironments": ["AzureCloud"]}, "parameters": {"effect": {"type": "String", "metadata": {"displayName": "Effect", "description": "Enable or disable the execution of the policy"}, "allowedValues": ["Audit", "Disabled", "<PERSON><PERSON>"], "defaultValue": "<PERSON><PERSON>"}}, "policyRule": {"if": {"allOf": [{"field": "type", "equals": "Microsoft.MachineLearningServices/workspaces"}, {"field": "Microsoft.MachineLearningServices/workspaces/publicNetworkAccess", "notEquals": "Disabled"}]}, "then": {"effect": "[parameters('effect')]"}}}}