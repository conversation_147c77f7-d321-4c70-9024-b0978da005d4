# Azure AD Groups Module

This module creates and manages Azure AD groups with role assignments for Azure RBAC and Azure AD directory roles.

## Features

- ✅ Create multiple Azure AD groups with flexible configuration
- ✅ Assign Azure RBAC roles at subscription level
- ✅ Assign Azure AD directory roles (with proper permissions)
- ✅ Support for role assignment to all subscriptions or current subscription only
- ✅ Configurable group properties (security, mail, role assignable)
- ✅ Automatic owner and member management
- ✅ Lifecycle protection to prevent accidental deletion

## Usage

### Basic Example

```hcl
module "azure_ad_groups" {
  source = "./modules/groups"

  groups = {
    infras_team_super_admin = {
      display_name         = "Infras Team - Super Admin"
      description          = "Infrastructure Team with Super Admin privileges"
      security_enabled     = true
      assignable_to_role   = true
      azure_roles          = ["Owner"]
      directory_roles      = ["Global Administrator"]
      members              = [
        "user1-object-id",
        "user2-object-id"
      ]
    }
  }

  assign_roles_to_all_subscriptions = false
  enable_directory_role_assignments = true
}
```

### Advanced Example

```hcl
module "azure_ad_groups" {
  source = "./modules/groups"

  groups = {
    infras_team_super_admin = {
      display_name         = "Infras Team - Super Admin"
      description          = "Infrastructure Team with Super Admin privileges"
      security_enabled     = true
      assignable_to_role   = true
      azure_roles          = ["Owner", "User Access Administrator"]
      directory_roles      = ["Global Administrator"]
      members              = ["user1-object-id", "user2-object-id"]
      additional_owners    = ["admin-user-object-id"]
    }
    
    developers = {
      display_name         = "Developers"
      description          = "Development team with contributor access"
      security_enabled     = true
      assignable_to_role   = false
      azure_roles          = ["Contributor"]
      members              = ["dev1-object-id", "dev2-object-id"]
    }
    
    readers = {
      display_name         = "Readers"
      description          = "Read-only access group"
      security_enabled     = true
      assignable_to_role   = false
      azure_roles          = ["Reader"]
    }
  }

  assign_roles_to_all_subscriptions = false
  enable_directory_role_assignments = true
  
  tags = {
    Environment = "Production"
    ManagedBy   = "Terraform"
  }
}
```

## Inputs

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|:--------:|
| groups | Map of Azure AD groups to create | `map(object)` | `{}` | no |
| assign_roles_to_all_subscriptions | Assign roles to all subscriptions | `bool` | `false` | no |
| enable_directory_role_assignments | Enable Azure AD directory role assignments | `bool` | `false` | no |
| tags | Tags to apply to resources | `map(string)` | `{}` | no |

### Group Object Structure

```hcl
{
  display_name         = string           # Required: Group display name
  description          = string           # Required: Group description
  security_enabled     = bool             # Required: Whether group is security enabled
  assignable_to_role   = bool             # Required: Whether group can be assigned to roles
  mail_enabled         = bool             # Optional: Whether group is mail enabled (default: false)
  members              = list(string)     # Optional: List of user object IDs (default: [])
  additional_owners    = list(string)     # Optional: Additional owners beyond current user (default: [])
  azure_roles          = list(string)     # Optional: Azure RBAC roles to assign (default: [])
  directory_roles      = list(string)     # Optional: Azure AD directory roles to assign (default: [])
}
```

## Outputs

| Name | Description |
|------|-------------|
| groups | Information about created groups |
| group_object_ids | Map of group keys to object IDs |
| azure_role_assignments | List of Azure RBAC role assignments |
| directory_role_assignments | List of directory role assignments |
| subscription_scopes | List of subscription scopes with role assignments |

## Requirements

### Permissions

- **Azure RBAC**: Owner or User Access Administrator role at subscription level
- **Azure AD**: Global Administrator or Privileged Role Administrator for directory roles

### Providers

- `azuread` ~> 2.47
- `azurerm` ~> 3.107

## Common Azure Roles

- `Owner` - Full access to all resources
- `Contributor` - Full access except role assignments
- `Reader` - Read-only access
- `User Access Administrator` - Manage user access to Azure resources

## Common Directory Roles

- `Global Administrator` - Full access to all Azure AD features
- `Privileged Role Administrator` - Manage role assignments
- `User Administrator` - Manage users and groups
- `Security Administrator` - Manage security features

## Notes

- Groups with `assignable_to_role = true` are required for directory role assignments
- Directory role assignments require elevated permissions
- The module includes lifecycle protection to prevent accidental group deletion
- Current user is automatically added as group owner
