# Variables for Azure AD Groups Module

variable "groups" {
  description = "Map of Azure AD groups to create with their configurations"
  type = map(object({
    display_name         = string
    description          = string
    security_enabled     = bool
    assignable_to_role   = bool
    mail_enabled         = optional(bool, false)
    members              = optional(list(string), [])
    additional_owners    = optional(list(string), [])
    azure_roles          = optional(list(string), [])
    directory_roles      = optional(list(string), [])
  }))
  default = {}

  validation {
    condition = alltrue([
      for group_key, group in var.groups :
      can(regex("^[a-zA-Z0-9\\s\\-_\\.]+$", group.display_name))
    ])
    error_message = "Group display names must contain only alphanumeric characters, spaces, hyphens, underscores, and periods."
  }
}

variable "assign_roles_to_all_subscriptions" {
  description = "Whether to assign Azure roles to all subscriptions in the tenant"
  type        = bool
  default     = false
}

variable "enable_directory_role_assignments" {
  description = "Whether to enable Azure AD directory role assignments"
  type        = bool
  default     = false
}

variable "tags" {
  description = "Tags to apply to resources where applicable"
  type        = map(string)
  default     = {}
}
