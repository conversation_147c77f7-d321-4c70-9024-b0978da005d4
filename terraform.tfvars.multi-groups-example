# Multi-Groups Configuration Example
# Copy this to terraform.tfvars and customize with your user object IDs

# Basic configuration
root_id   = "myorg"
root_name = "My Organization"

# Multiple Azure AD Groups configuration
azure_ad_groups = {
  # ===== INFRASTRUCTURE TEAMS =====
  infras_team_super_admin = {
    display_name         = "Infras Team - Super Admin"
    description          = "Infrastructure Team with Super Admin privileges including Owner, Enterprise Administrator, and EA Purchaser roles"
    security_enabled     = true
    assignable_to_role   = true              # Required for Global Administrator
    azure_roles          = ["Owner", "User Access Administrator"]
    directory_roles      = ["Global Administrator"]
    members = [
      # "admin1-object-id",                  # Replace with actual user object IDs
      # "admin2-object-id",
    ]
    additional_owners = [
      # "manager-object-id",                 # Additional owners if needed
    ]
  }
  
  infras_team_contributors = {
    display_name         = "Infras Team - Contributors"
    description          = "Infrastructure Team with Contributor access to manage resources"
    security_enabled     = true
    assignable_to_role   = false             # No directory roles needed
    azure_roles          = ["Contributor", "Network Contributor", "Storage Account Contributor"]
    members = [
      # "infra1-object-id",
      # "infra2-object-id",
      # "infra3-object-id",
    ]
  }
  
  # ===== DEVELOPMENT TEAMS =====
  developers_senior = {
    display_name         = "Developers - Senior"
    description          = "Senior development team with elevated access"
    security_enabled     = true
    assignable_to_role   = false
    azure_roles          = ["Contributor", "Key Vault Contributor"]
    members = [
      # "senior-dev1-object-id",
      # "senior-dev2-object-id",
      # "senior-dev3-object-id",
    ]
  }
  
  developers_junior = {
    display_name         = "Developers - Junior"
    description          = "Junior development team with limited access"
    security_enabled     = true
    assignable_to_role   = false
    azure_roles          = ["Reader", "Storage Blob Data Reader"]
    members = [
      # "junior-dev1-object-id",
      # "junior-dev2-object-id",
      # "junior-dev3-object-id",
    ]
  }
  
  # ===== SECURITY TEAM =====
  security_admins = {
    display_name         = "Security - Administrators"
    description          = "Security team with administrative privileges"
    security_enabled     = true
    assignable_to_role   = true              # Required for Security Administrator
    azure_roles          = ["Security Admin", "Key Vault Administrator"]
    directory_roles      = ["Security Administrator"]
    members = [
      # "security-admin1-object-id",
      # "security-admin2-object-id",
    ]
  }
  
  security_readers = {
    display_name         = "Security - Readers"
    description          = "Security team with read-only access"
    security_enabled     = true
    assignable_to_role   = true              # Required for Security Reader
    azure_roles          = ["Security Reader"]
    directory_roles      = ["Security Reader"]
    members = [
      # "security-reader1-object-id",
      # "security-reader2-object-id",
    ]
  }
  
  # ===== BUSINESS TEAMS =====
  finance_team = {
    display_name         = "Finance Team"
    description          = "Finance team with billing and cost management access"
    security_enabled     = true
    assignable_to_role   = false
    azure_roles          = ["Cost Management Reader", "Billing Reader"]
    members = [
      # "finance1-object-id",
      # "finance2-object-id",
    ]
  }
  
  hr_team = {
    display_name         = "HR Team"
    description          = "Human Resources team with user management access"
    security_enabled     = true
    assignable_to_role   = true              # Required for User Administrator
    azure_roles          = ["Reader"]
    directory_roles      = ["User Administrator"]
    members = [
      # "hr1-object-id",
      # "hr2-object-id",
    ]
  }
  
  # ===== PROJECT TEAMS =====
  project_alpha_team = {
    display_name         = "Project Alpha Team"
    description          = "Team working on Project Alpha with dedicated resources"
    security_enabled     = true
    assignable_to_role   = false
    azure_roles          = ["Contributor"]
    members = [
      # "alpha-lead-object-id",
      # "alpha-dev1-object-id",
      # "alpha-dev2-object-id",
    ]
  }
  
  project_beta_team = {
    display_name         = "Project Beta Team"
    description          = "Team working on Project Beta with dedicated resources"
    security_enabled     = true
    assignable_to_role   = false
    azure_roles          = ["Contributor"]
    members = [
      # "beta-lead-object-id",
      # "beta-dev1-object-id",
    ]
  }
  
  # ===== READ-ONLY TEAMS =====
  business_users = {
    display_name         = "Business Users"
    description          = "Business users with read-only access to reports and dashboards"
    security_enabled     = true
    assignable_to_role   = false
    azure_roles          = ["Reader"]
    members = [
      # "business-user1-object-id",
      # "business-user2-object-id",
      # "business-user3-object-id",
    ]
  }
  
  external_auditors = {
    display_name         = "External Auditors"
    description          = "External auditors with limited read access"
    security_enabled     = true
    assignable_to_role   = false
    azure_roles          = ["Reader"]
    members = [
      # "auditor1-object-id",
      # "auditor2-object-id",
    ]
  }
}

# Role assignment scope
# Set to true to assign Azure roles to all subscriptions in the tenant
# Set to false to assign only to the current subscription
assign_roles_to_all_subscriptions = false

# Directory role assignments
# Set to true to enable Azure AD directory role assignments
# Requires Privileged Role Administrator permissions
enable_directory_role_assignments = false

# How to get user object IDs:
# az ad user show --id <EMAIL> --query id -o tsv
# az ad signed-in-user show --query id -o tsv
# az ad user list --query "[].{DisplayName:displayName, ObjectId:id}" -o table
