# Azure AD Groups Configuration Guide

## Group Object Structure

```hcl
group_name = {
  display_name         = string           # Required: <PERSON><PERSON><PERSON> hiển thị của group
  description          = string           # Required: <PERSON><PERSON> tả group
  security_enabled     = bool             # Required: true cho security group
  assignable_to_role   = bool             # Required: true n<PERSON><PERSON> c<PERSON>n assign directory roles
  mail_enabled         = bool             # Optional: false (default)
  members              = list(string)     # Optional: List user object IDs
  additional_owners    = list(string)     # Optional: Additional owners
  azure_roles          = list(string)     # Optional: Azure RBAC roles
  directory_roles      = list(string)     # Optional: Azure AD directory roles
}
```

## Common Group Templates

### 1. Infrastructure/Admin Teams
```hcl
infras_team_super_admin = {
  display_name         = "Infras Team - Super Admin"
  description          = "Infrastructure Team with Super Admin privileges"
  security_enabled     = true
  assignable_to_role   = true              # Required for Global Administrator
  azure_roles          = ["Owner", "User Access Administrator"]
  directory_roles      = ["Global Administrator"]
  members = [
    "admin1-object-id",
    "admin2-object-id"
  ]
}

infras_team_contributors = {
  display_name         = "Infras Team - Contributors"
  description          = "Infrastructure Team with Contributor access"
  security_enabled     = true
  assignable_to_role   = false             # No directory roles needed
  azure_roles          = ["Contributor", "Network Contributor"]
  members = [
    "infra1-object-id",
    "infra2-object-id"
  ]
}
```

### 2. Development Teams
```hcl
developers_senior = {
  display_name         = "Senior Developers"
  description          = "Senior development team with elevated access"
  security_enabled     = true
  assignable_to_role   = false
  azure_roles          = ["Contributor", "Key Vault Contributor"]
  members = [
    "senior-dev1-object-id",
    "senior-dev2-object-id"
  ]
}

developers_junior = {
  display_name         = "Junior Developers"
  description          = "Junior development team with limited access"
  security_enabled     = true
  assignable_to_role   = false
  azure_roles          = ["Reader", "Storage Blob Data Reader"]
  members = [
    "junior-dev1-object-id",
    "junior-dev2-object-id"
  ]
}
```

### 3. Security Teams
```hcl
security_admins = {
  display_name         = "Security Administrators"
  description          = "Security team with admin privileges"
  security_enabled     = true
  assignable_to_role   = true              # Required for Security Administrator
  azure_roles          = ["Security Admin", "Key Vault Administrator"]
  directory_roles      = ["Security Administrator"]
  members = [
    "security-admin1-object-id",
    "security-admin2-object-id"
  ]
}

security_readers = {
  display_name         = "Security Readers"
  description          = "Security team with read-only access"
  security_enabled     = true
  assignable_to_role   = true              # Required for Security Reader
  azure_roles          = ["Security Reader"]
  directory_roles      = ["Security Reader"]
  members = [
    "security-reader1-object-id",
    "security-reader2-object-id"
  ]
}
```

### 4. Business Teams
```hcl
finance_team = {
  display_name         = "Finance Team"
  description          = "Finance team with billing and cost management access"
  security_enabled     = true
  assignable_to_role   = false
  azure_roles          = ["Cost Management Reader", "Billing Reader"]
  members = [
    "finance1-object-id",
    "finance2-object-id"
  ]
}

hr_team = {
  display_name         = "HR Team"
  description          = "Human Resources team"
  security_enabled     = true
  assignable_to_role   = true              # Required for User Administrator
  azure_roles          = ["Reader"]
  directory_roles      = ["User Administrator"]
  members = [
    "hr1-object-id",
    "hr2-object-id"
  ]
}
```

### 5. Project-Specific Teams
```hcl
project_alpha_team = {
  display_name         = "Project Alpha Team"
  description          = "Team working on Project Alpha"
  security_enabled     = true
  assignable_to_role   = false
  azure_roles          = ["Contributor"]
  members = [
    "alpha-lead-object-id",
    "alpha-dev1-object-id",
    "alpha-dev2-object-id"
  ]
}

project_beta_team = {
  display_name         = "Project Beta Team"
  description          = "Team working on Project Beta"
  security_enabled     = true
  assignable_to_role   = false
  azure_roles          = ["Contributor"]
  members = [
    "beta-lead-object-id",
    "beta-dev1-object-id"
  ]
}
```

## Common Azure RBAC Roles

### Administrative Roles
- `Owner` - Full access including role assignments
- `Contributor` - Full access except role assignments
- `User Access Administrator` - Manage user access only

### Security Roles
- `Security Admin` - Manage security features
- `Security Reader` - Read security information
- `Key Vault Administrator` - Manage Key Vault
- `Key Vault Contributor` - Manage Key Vault except permissions

### Networking Roles
- `Network Contributor` - Manage networks
- `DNS Zone Contributor` - Manage DNS zones
- `Private DNS Zone Contributor` - Manage private DNS

### Storage Roles
- `Storage Account Contributor` - Manage storage accounts
- `Storage Blob Data Contributor` - Read/write blob data
- `Storage Blob Data Reader` - Read blob data

### Monitoring Roles
- `Monitoring Contributor` - Manage monitoring resources
- `Monitoring Reader` - Read monitoring data
- `Log Analytics Contributor` - Manage Log Analytics

## Common Directory Roles

### Administrative Roles
- `Global Administrator` - Full access to all Azure AD features
- `Privileged Role Administrator` - Manage role assignments
- `User Administrator` - Manage users and groups

### Security Roles
- `Security Administrator` - Manage security features
- `Security Reader` - Read security information
- `Conditional Access Administrator` - Manage conditional access

### Application Roles
- `Application Administrator` - Manage applications
- `Cloud Application Administrator` - Manage cloud applications

## Getting User Object IDs

### Using Azure CLI
```bash
# Get current user
az ad signed-in-user show --query id -o tsv

# Get specific user by email
az ad user show --id <EMAIL> --query id -o tsv

# Get specific user by UPN
az ad user show --upn <EMAIL> --query id -o tsv

# List all users
az ad user list --query "[].{DisplayName:displayName, ObjectId:id, UserPrincipalName:userPrincipalName}" -o table
```

### Using PowerShell
```powershell
# Get current user
(Get-AzContext).Account.ExtendedProperties.HomeAccountId.Split('.')[0]

# Get specific user
(Get-AzADUser -UserPrincipalName <EMAIL>).Id

# List all users
Get-AzADUser | Select-Object DisplayName, Id, UserPrincipalName
```

## Best Practices

### 1. Naming Convention
- Use descriptive names: `"Team Name - Role Level"`
- Examples: `"Developers - Senior"`, `"Security - Admins"`

### 2. Role Assignment Strategy
- **Principle of Least Privilege**: Give minimum required access
- **Separation of Duties**: Different teams for different functions
- **Regular Review**: Periodically review group memberships

### 3. Group Organization
- **By Function**: Development, Security, Infrastructure
- **By Project**: Project Alpha, Project Beta
- **By Access Level**: Admin, Contributor, Reader

### 4. Security Considerations
- Set `assignable_to_role = true` only when needed for directory roles
- Use additional_owners for backup administrators
- Regular audit of group memberships

## Example Complete Configuration

```hcl
azure_ad_groups = {
  # Infrastructure Teams
  infras_super_admin = {
    display_name         = "Infrastructure - Super Admin"
    description          = "Infrastructure super administrators"
    security_enabled     = true
    assignable_to_role   = true
    azure_roles          = ["Owner"]
    directory_roles      = ["Global Administrator"]
    members              = ["admin1-id", "admin2-id"]
  }
  
  infras_contributors = {
    display_name         = "Infrastructure - Contributors"
    description          = "Infrastructure contributors"
    security_enabled     = true
    assignable_to_role   = false
    azure_roles          = ["Contributor", "Network Contributor"]
    members              = ["infra1-id", "infra2-id", "infra3-id"]
  }
  
  # Development Teams
  developers_senior = {
    display_name         = "Developers - Senior"
    description          = "Senior development team"
    security_enabled     = true
    assignable_to_role   = false
    azure_roles          = ["Contributor"]
    members              = ["senior-dev1-id", "senior-dev2-id"]
  }
  
  developers_junior = {
    display_name         = "Developers - Junior"
    description          = "Junior development team"
    security_enabled     = true
    assignable_to_role   = false
    azure_roles          = ["Reader"]
    members              = ["junior-dev1-id", "junior-dev2-id"]
  }
  
  # Security Team
  security_team = {
    display_name         = "Security Team"
    description          = "Security and compliance team"
    security_enabled     = true
    assignable_to_role   = true
    azure_roles          = ["Security Admin"]
    directory_roles      = ["Security Administrator"]
    members              = ["security1-id", "security2-id"]
  }
  
  # Business Teams
  finance_team = {
    display_name         = "Finance Team"
    description          = "Finance and billing team"
    security_enabled     = true
    assignable_to_role   = false
    azure_roles          = ["Cost Management Reader", "Billing Reader"]
    members              = ["finance1-id", "finance2-id"]
  }
}
```
