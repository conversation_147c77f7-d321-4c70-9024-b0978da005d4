{"Modules": [{"Key": "", "Source": "", "Dir": "."}, {"Key": "azure_ad_groups", "Source": "./modules/azure_ad_groups", "Dir": "modules/azure_ad_groups"}, {"Key": "enterprise_scale", "Source": "registry.terraform.io/Azure/caf-enterprise-scale/azurerm", "Version": "6.3.1", "Dir": ".terraform/modules/enterprise_scale"}, {"Key": "enterprise_scale.connectivity_resources", "Source": "./modules/connectivity", "Dir": ".terraform/modules/enterprise_scale/modules/connectivity"}, {"Key": "enterprise_scale.identity_resources", "Source": "./modules/identity", "Dir": ".terraform/modules/enterprise_scale/modules/identity"}, {"Key": "enterprise_scale.management_group_archetypes", "Source": "./modules/archetypes", "Dir": ".terraform/modules/enterprise_scale/modules/archetypes"}, {"Key": "enterprise_scale.management_resources", "Source": "./modules/management", "Dir": ".terraform/modules/enterprise_scale/modules/management"}, {"Key": "enterprise_scale.role_assignments_for_policy", "Source": "./modules/role_assignments_for_policy", "Dir": ".terraform/modules/enterprise_scale/modules/role_assignments_for_policy"}, {"Key": "groups", "Source": "./modules/groups", "Dir": "modules/groups"}]}