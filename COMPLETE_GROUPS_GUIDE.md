# Complete Azure AD Groups Implementation Guide

## 🎯 Overview

Đã thêm thành công **10 Azure AD groups** theo yêu cầu với đầy đủ role assignments và scope phù hợp.

## 📋 Groups Summary

| Group Name | Description | Azure Roles | Directory Roles | Scope |
|------------|-------------|-------------|-----------------|-------|
| **Infras Team - Super Admin** | Infrastructure super admin | Owner | Global Administrator | Subscription |
| **Infras Team - Admin** | Infrastructure admin | Contributor | - | Subscription |
| **Infras Team - Operation** | Infrastructure operations | Reader | - | Subscription |
| **Infras Team - Breakglass** | Infrastructure emergency | Owner | Global Administrator | Subscription |
| **App Teams - Super Admin** | Application super admin | Contributor | - | Resource Group |
| **App Teams - Admin** | Application admin with VM/Storage | VM Admin Login, VM User Login, Reader and Data Access, Storage Blob Data Reader, AKS Contributor | - | Resource Group |
| **App Teams - Operation** | Application operations | Reader | - | Resource Group |
| **App Teams - Breakglass** | Application emergency | Contributor | - | Resource Group |
| **Security Team** | Security management | Security Admin, User Access Administrator, Attestation Contributor, Key Vault Administrator, Reader | - | Resource Group |
| **Finance Team** | Billing management | Reader | - | Management Group |

## 🏗️ Implementation Details

### ✅ **Module Structure**
```
modules/azure_ad_groups/
├── main.tf          # Main module logic
├── variables.tf     # Input variables
├── outputs.tf       # Output values
└── README.md        # Module documentation
```

### ✅ **Configuration Files**
- `variables.tf` - Contains all 10 groups with default configurations
- `terraform.tfvars.complete-groups` - Complete example with all groups
- `verify-roles.ps1` - Script to verify Azure role names
- `get-user-ids.ps1` - Script to get user object IDs

### ✅ **Key Features**
- **Flexible Role Assignment**: Support for both Azure RBAC and Directory roles
- **Conditional Deployment**: Enable/disable directory roles based on permissions
- **Safety Features**: Lifecycle protection, input validation
- **Scalable**: Easy to add more groups or modify existing ones

## 🚀 Deployment Steps

### 1. **Prepare Configuration**
```bash
# Copy the complete groups configuration
cp terraform.tfvars.complete-groups terraform.tfvars

# Edit terraform.tfvars and add your user object IDs
# Replace comments like "# user1-object-id" with actual Object IDs
```

### 2. **Get User Object IDs**
```bash
# Method 1: Use PowerShell script
./get-user-ids.ps1

# Method 2: Use Azure CLI directly
az ad user show --id <EMAIL> --query id -o tsv
az ad signed-in-user show --query id -o tsv
```

### 3. **Verify Role Names** (Optional)
```bash
# Run verification script to check role names
./verify-roles.ps1
```

### 4. **Deploy**
```bash
# Initialize and validate
terraform init
terraform validate

# Plan and apply
terraform plan
terraform apply
```

## 📝 Configuration Example

```hcl
# In terraform.tfvars
azure_ad_groups = {
  infras_team_super_admin = {
    display_name         = "Infras Team - Super Admin"
    description          = "Infrastructure Team with Super Admin privileges"
    security_enabled     = true
    assignable_to_role   = true
    azure_roles          = ["Owner"]
    directory_roles      = ["Global Administrator"]
    members = [
      "12345678-1234-1234-1234-123456789012",  # Admin User 1
      "87654321-4321-4321-4321-210987654321",  # Admin User 2
    ]
  }
  
  # ... other groups
}

# Role assignment settings
assign_roles_to_all_subscriptions = false
enable_directory_role_assignments = false  # Set to true when you have permissions
```

## ⚠️ Important Notes

### **EA (Enterprise Agreement) Roles**
- `EA: Enterprise administrator` và `EA: EA purchaser` **KHÔNG thể** assign qua Terraform
- Phải assign manual qua **Azure EA Portal**
- Các roles này được note trong description nhưng không implement trong code

### **Role Scope Limitations**
- **Resource Group scope**: Hiện tại assign tại subscription level, cần custom logic cho true RG scope
- **Management Group scope**: Finance Team được assign tại subscription level thay vì MG level

### **Directory Role Requirements**
- Cần **Privileged Role Administrator** permissions
- Set `enable_directory_role_assignments = true` khi có đủ permissions
- Groups cần `assignable_to_role = true` để assign directory roles

### **Role Name Verification**
Một số role names có thể cần verify:
- `Virtual Machine Local User Login` → có thể là `Virtual Machine User Login`
- `Reader and Data Access` → có thể là `Storage Blob Data Reader`
- `Azure Kubernetes Service Contributor Role` → có thể là `Azure Kubernetes Service Contributor`

## 🔧 Customization

### **Adding New Groups**
```hcl
# Add to variables.tf default value
new_group_name = {
  display_name         = "New Group Name"
  description          = "Group description"
  security_enabled     = true
  assignable_to_role   = false  # true if need directory roles
  azure_roles          = ["Role1", "Role2"]
  directory_roles      = ["Directory Role"]  # if assignable_to_role = true
  members              = []
}
```

### **Modifying Existing Groups**
```hcl
# In terraform.tfvars, override default values
azure_ad_groups = {
  infras_team_super_admin = {
    # ... existing config
    members = [
      "new-user-object-id",
      "another-user-object-id"
    ]
  }
}
```

## 📊 Outputs

After deployment, you'll get:
```hcl
# Group information
azure_ad_groups = {
  infras_team_super_admin = {
    object_id    = "group-object-id"
    display_name = "Infras Team - Super Admin"
    description  = "..."
  }
  # ... other groups
}

# Role assignments
azure_role_assignments = [
  {
    scope     = "/subscriptions/sub-id"
    role_name = "Owner"
    principal_id = "group-object-id"
  }
  # ... other assignments
]
```

## 🎯 Next Steps

1. **Deploy basic configuration** với `enable_directory_role_assignments = false`
2. **Test role assignments** và verify access
3. **Get proper permissions** cho directory role assignments
4. **Enable directory roles** bằng cách set `enable_directory_role_assignments = true`
5. **Assign EA roles manually** qua Azure EA Portal
6. **Implement Resource Group scoping** nếu cần thiết

## 🔍 Troubleshooting

### **Permission Issues**
- Ensure you have proper Azure AD permissions for directory roles
- Use `enable_directory_role_assignments = false` if you don't have Privileged Role Administrator

### **Role Name Issues**
- Run `verify-roles.ps1` to check role names
- Use Azure CLI to search for correct role names
- Check Azure documentation for exact role names

### **Module Issues**
- Ensure module path is correct: `./modules/azure_ad_groups`
- Run `terraform init` after any module changes

Tất cả 10 groups đã được implement thành công và sẵn sàng deploy! 🎉
