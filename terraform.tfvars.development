# Development Environment Configuration
# Use this file for development deployment

root_id   = "myorg-dev"
root_name = "My Organization - Development"

# Development Azure AD Groups
groups = {
  infras_team_super_admin = {
    display_name         = "Infras Team - Super Admin (Dev)"
    description          = "Infrastructure Team with Super Admin privileges for Development"
    security_enabled     = true
    assignable_to_role   = true
    azure_roles          = ["Owner"]
    directory_roles      = ["Global Administrator"]
    members = [
      # Add development admin user IDs here
    ]
  }

  development_developers = {
    display_name         = "Development Team"
    description          = "Development team with full contributor access for testing"
    security_enabled     = true
    assignable_to_role   = false
    azure_roles          = ["Contributor"]
    members = [
      # Add development team user IDs here
    ]
  }

  development_testers = {
    display_name         = "Development Testers"
    description          = "Testing team with reader access for validation"
    security_enabled     = true
    assignable_to_role   = false
    azure_roles          = ["Reader"]
    members = [
      # Add testing team user IDs here
    ]
  }

  development_devops = {
    display_name         = "Development DevOps"
    description          = "DevOps team with automation privileges for development"
    security_enabled     = true
    assignable_to_role   = false
    azure_roles          = ["Contributor", "User Access Administrator"]
    members = [
      # Add DevOps team user IDs here
    ]
  }

  development_security = {
    display_name         = "Development Security"
    description          = "Security team for development environment"
    security_enabled     = true
    assignable_to_role   = true
    azure_roles          = ["Security Administrator"]
    directory_roles      = ["Security Administrator"]
    members = [
      # Add development security team user IDs here
    ]
  }
}

# Development settings
assign_roles_to_all_subscriptions = false
enable_directory_role_assignments = false 